* { box-sizing: border-box; }
body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; margin: 0; background: #0b1220; color: #e6eef8; }
.container { max-width: 820px; margin: 40px auto; padding: 0 16px; }
h1 { margin-top: 0; font-weight: 700; }
.row { display: flex; gap: 12px; align-items: center; margin-bottom: 10px; }
label { width: 90px; color: #a9b7d0; }
input { flex: 1; padding: 10px 12px; border: 1px solid #2a3652; border-radius: 8px; background: #111a2e; color: #e6eef8; }
button { padding: 10px 16px; background: #1f6feb; color: white; border: 0; border-radius: 8px; cursor: pointer; }
button:hover { background: #2b77ee; }
.output { margin-top: 20px; background: #0d1626; border: 1px solid #2a3652; border-radius: 8px; padding: 12px; min-height: 140px; overflow: auto; }
