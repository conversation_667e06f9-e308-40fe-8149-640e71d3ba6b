# 🚀 Guide Rapide - Application BingX Trading

## ✅ Installation Terminée !

Votre application BingX est maintenant prête à utiliser avec vos clés API configurées.

## 🔧 Commandes Essentielles

### Test de Connexion
```bash
cd /workspaces/TradingAgents/bingxapp
make test
```

### Informations du Compte
```bash
make info
```

### Données de March<PERSON>
```bash
make market          # BTC-USDT
```

### Positions Ouvertes
```bash
make positions
```

### Monitoring en Temps Réel
```bash
make monitor         # BTC-USDT et ETH-USDT
make monitor-btc     # BTC-USDT uniquement
```

### Exemples Interactifs
```bash
make examples
```

## 📊 Analyses Avancées

### Analyse des Positions
```bash
make analyze
```

### Carnet d'Ordres
```bash
make orderbook
```

## ⚠️ Trading (Attention !)

### Ordre au Marché
```bash
make order-market SYMBOL=BTC-USDT SIDE=BUY QTY=0.001
```

### Ordre Limite
```bash
make order-limit SYMBOL=BTC-USDT SIDE=BUY QTY=0.001 PRICE=50000
```

## 📚 Aide

### Toutes les Commandes
```bash
make help
```

### Aide Trading
```bash
make help-trading
```

### Aide Exemples
```bash
make help-examples
```

## 🔍 Utilisation Programmatique

```python
from bingxapp.config import BingXConfig
from bingxapp.trading_bot import TradingBot

# Initialiser le bot
config = BingXConfig.from_env()
bot = TradingBot(config)

if bot.initialize():
    # Récupérer le solde
    balance = bot.get_account_balance()
    
    # Données de marché
    market_data = bot.get_market_data("BTC-USDT")
    
    # Positions
    positions = bot.get_positions()
    
    # Placer un ordre (attention !)
    # order = bot.place_market_order("BTC-USDT", "BUY", 0.001)
```

## 📁 Structure des Fichiers

```
bingxapp/
├── __init__.py          # Module principal
├── config.py            # Configuration et clés API
├── client.py            # Client API BingX
├── trading_bot.py       # Bot de trading
├── main.py              # Interface CLI
├── examples.py          # Exemples d'utilisation
├── test_app.py          # Tests
├── setup.py             # Installation
├── Makefile             # Commandes rapides
├── requirements.txt     # Dépendances
├── .env.example         # Configuration exemple
├── README.md            # Documentation complète
└── GUIDE_RAPIDE.md      # Ce guide
```

## 🔐 Sécurité

- ✅ Clés API configurées et fonctionnelles
- ⚠️ Les clés sont actuellement en dur dans le code
- 💡 Pour la production, utilisez des variables d'environnement
- 🔒 Testez toujours avec de petites quantités

## 🎯 Prochaines Étapes

1. **Familiarisez-vous** avec les commandes de base
2. **Testez** les exemples interactifs
3. **Surveillez** vos positions avec le monitoring
4. **Développez** vos propres stratégies
5. **Automatisez** avec des scripts personnalisés

## 🆘 Support

- 📖 Consultez le `README.md` pour plus de détails
- 🧪 Utilisez `make test-full` pour diagnostiquer les problèmes
- 📊 Les logs détaillés vous aideront à déboguer
- 🔍 Vérifiez la connectivité avec `make test`

---

**🎉 Félicitations ! Votre application BingX Trading est opérationnelle !**
