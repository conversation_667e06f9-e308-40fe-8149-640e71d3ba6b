#!/usr/bin/env python3
"""
Suite de tests complète pour l'application BingX Trading Enhanced
"""

import asyncio
import json
import time
import requests
import subprocess
import sys
import os
from datetime import datetime
from pathlib import Path

class TestSuite:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = []
        self.start_time = time.time()
        
    def log_test(self, test_name, success, message="", duration=0):
        """Enregistre le résultat d'un test"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        print(f"{status} {test_name}{duration_str}")
        if message:
            print(f"   {message}")
    
    def test_server_health(self):
        """Test de santé du serveur"""
        start = time.time()
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            duration = time.time() - start
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("Server Health", True, 
                             f"Status: {data.get('status')}, Version: {data.get('version')}", 
                             duration)
                return True
            else:
                self.log_test("Server Health", False, 
                             f"Status code: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start
            self.log_test("Server Health", False, str(e), duration)
            return False
    
    def test_authentication(self):
        """Test d'authentification"""
        start = time.time()
        try:
            auth_data = {"username": "admin", "password": "admin123"}
            response = requests.post(f"{self.base_url}/api/auth/login", data=auth_data)
            duration = time.time() - start
            
            if response.status_code == 200:
                token_data = response.json()
                self.auth_token = token_data.get("access_token")
                self.log_test("Authentication", True, "Token received", duration)
                return True
            else:
                self.log_test("Authentication", False, 
                             f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start
            self.log_test("Authentication", False, str(e), duration)
            return False
    
    def test_protected_endpoints(self):
        """Test des endpoints protégés"""
        if not hasattr(self, 'auth_token'):
            self.log_test("Protected Endpoints", False, "No auth token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        endpoints = [
            ("/api/metrics", "Metrics"),
            ("/api/websocket/stats", "WebSocket Stats"),
            ("/api/security/status", "Security Status")
        ]
        
        all_success = True
        for endpoint, name in endpoints:
            start = time.time()
            try:
                response = requests.get(f"{self.base_url}{endpoint}", headers=headers)
                duration = time.time() - start
                
                if response.status_code == 200:
                    self.log_test(f"Endpoint {name}", True, "", duration)
                else:
                    self.log_test(f"Endpoint {name}", False, 
                                 f"Status: {response.status_code}", duration)
                    all_success = False
            except Exception as e:
                duration = time.time() - start
                self.log_test(f"Endpoint {name}", False, str(e), duration)
                all_success = False
        
        return all_success
    
    def test_static_files(self):
        """Test des fichiers statiques"""
        static_files = [
            "/static/css/style.css",
            "/static/js/trading.js",
            "/static/js/notifications.js",
            "/static/js/theme-manager.js",
            "/static/js/performance.js",
            "/static/js/advanced-charts.js",
            "/static/js/price-alerts.js",
            "/static/js/advanced-orders.js",
            "/static/js/security-monitor.js"
        ]
        
        all_success = True
        for file_path in static_files:
            start = time.time()
            try:
                response = requests.get(f"{self.base_url}{file_path}")
                duration = time.time() - start
                
                if response.status_code == 200:
                    self.log_test(f"Static File {file_path}", True, "", duration)
                else:
                    self.log_test(f"Static File {file_path}", False, 
                                 f"Status: {response.status_code}", duration)
                    all_success = False
            except Exception as e:
                duration = time.time() - start
                self.log_test(f"Static File {file_path}", False, str(e), duration)
                all_success = False
        
        return all_success
    
    def test_pages(self):
        """Test des pages principales"""
        pages = [
            ("/", "Home Page"),
            ("/dashboard", "Dashboard Enhanced"),
            ("/dashboard/classic", "Dashboard Classic")
        ]
        
        all_success = True
        for path, name in pages:
            start = time.time()
            try:
                response = requests.get(f"{self.base_url}{path}")
                duration = time.time() - start
                
                if response.status_code == 200:
                    # Vérifier la présence d'éléments clés
                    content = response.text
                    checks = [
                        ("BingX Trading" in content, "Title present"),
                        ("static/js/trading.js" in content, "Scripts loaded"),
                        ("static/css/style.css" in content, "Styles loaded")
                    ]
                    
                    all_checks_passed = all(check[0] for check in checks)
                    failed_checks = [check[1] for check in checks if not check[0]]
                    
                    if all_checks_passed:
                        self.log_test(f"Page {name}", True, "All checks passed", duration)
                    else:
                        self.log_test(f"Page {name}", False, 
                                     f"Failed: {', '.join(failed_checks)}", duration)
                        all_success = False
                else:
                    self.log_test(f"Page {name}", False, 
                                 f"Status: {response.status_code}", duration)
                    all_success = False
            except Exception as e:
                duration = time.time() - start
                self.log_test(f"Page {name}", False, str(e), duration)
                all_success = False
        
        return all_success
    
    def test_websocket_connection(self):
        """Test de connexion WebSocket"""
        try:
            import websocket
            
            start = time.time()
            connected = False
            message_received = False
            
            def on_message(ws, message):
                nonlocal message_received
                message_received = True
                ws.close()
            
            def on_error(ws, error):
                pass
            
            def on_close(ws, close_status_code, close_msg):
                pass
            
            def on_open(ws):
                nonlocal connected
                connected = True
            
            ws = websocket.WebSocketApp(
                f"ws://localhost:8000/ws",
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )
            
            # Lancer dans un thread avec timeout
            import threading
            thread = threading.Thread(target=ws.run_forever)
            thread.daemon = True
            thread.start()
            
            # Attendre la connexion
            timeout = 5
            while not connected and timeout > 0:
                time.sleep(0.1)
                timeout -= 0.1
            
            if connected:
                # Attendre un message
                timeout = 3
                while not message_received and timeout > 0:
                    time.sleep(0.1)
                    timeout -= 0.1
                
                duration = time.time() - start
                if message_received:
                    self.log_test("WebSocket Connection", True, "Message received", duration)
                    return True
                else:
                    self.log_test("WebSocket Connection", True, "Connected but no message", duration)
                    return True
            else:
                duration = time.time() - start
                self.log_test("WebSocket Connection", False, "Connection failed", duration)
                return False
                
        except ImportError:
            self.log_test("WebSocket Connection", False, "websocket-client not installed")
            return False
        except Exception as e:
            duration = time.time() - start
            self.log_test("WebSocket Connection", False, str(e), duration)
            return False
    
    def test_performance(self):
        """Test de performance basique"""
        start = time.time()
        
        # Test de charge basique
        response_times = []
        for i in range(10):
            req_start = time.time()
            try:
                response = requests.get(f"{self.base_url}/api/health")
                req_duration = time.time() - req_start
                response_times.append(req_duration)
                
                if response.status_code != 200:
                    self.log_test("Performance Test", False, f"Request {i+1} failed")
                    return False
            except Exception as e:
                self.log_test("Performance Test", False, f"Request {i+1} error: {e}")
                return False
        
        total_duration = time.time() - start
        avg_response_time = sum(response_times) / len(response_times)
        
        # Critères de performance
        if avg_response_time < 0.5:  # Moins de 500ms en moyenne
            self.log_test("Performance Test", True, 
                         f"Avg response: {avg_response_time:.3f}s", total_duration)
            return True
        else:
            self.log_test("Performance Test", False, 
                         f"Slow avg response: {avg_response_time:.3f}s", total_duration)
            return False
    
    def test_file_structure(self):
        """Test de la structure des fichiers"""
        required_files = [
            "enhanced_app.py",
            "web_config.py",
            "auth.py",
            "static/css/style.css",
            "static/js/trading.js",
            "static/js/notifications.js",
            "static/js/theme-manager.js",
            "static/js/performance.js",
            "static/js/advanced-charts.js",
            "static/js/price-alerts.js",
            "static/js/advanced-orders.js",
            "static/js/security-monitor.js",
            "templates/base.html",
            "templates/dashboard_enhanced.html",
            "AMELIORATIONS.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if not missing_files:
            self.log_test("File Structure", True, f"All {len(required_files)} files present")
            return True
        else:
            self.log_test("File Structure", False, 
                         f"Missing: {', '.join(missing_files[:3])}{'...' if len(missing_files) > 3 else ''}")
            return False
    
    def run_all_tests(self):
        """Exécute tous les tests"""
        print("🧪 BingX Trading Enhanced - Suite de Tests")
        print("=" * 60)
        print(f"🕒 Début des tests: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Tests de structure (ne nécessitent pas le serveur)
        print("📁 Tests de structure...")
        self.test_file_structure()
        
        # Tests nécessitant le serveur
        print("\n🌐 Tests du serveur...")
        if not self.test_server_health():
            print("❌ Serveur non accessible - Tests interrompus")
            self.generate_report()
            return False
        
        print("\n🔐 Tests d'authentification...")
        self.test_authentication()
        
        print("\n🛡️ Tests des endpoints protégés...")
        self.test_protected_endpoints()
        
        print("\n📄 Tests des pages...")
        self.test_pages()
        
        print("\n📦 Tests des fichiers statiques...")
        self.test_static_files()
        
        print("\n🔌 Tests WebSocket...")
        self.test_websocket_connection()
        
        print("\n⚡ Tests de performance...")
        self.test_performance()
        
        self.generate_report()
        return True
    
    def generate_report(self):
        """Génère un rapport de tests"""
        total_duration = time.time() - self.start_time
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "📊" + "=" * 50 + "📊")
        print("RAPPORT DE TESTS")
        print("=" * 54)
        print(f"✅ Tests réussis: {passed_tests}")
        print(f"❌ Tests échoués: {failed_tests}")
        print(f"📊 Total: {total_tests}")
        print(f"📈 Taux de réussite: {(passed_tests/total_tests*100):.1f}%")
        print(f"⏱️  Durée totale: {total_duration:.2f}s")
        
        if failed_tests > 0:
            print("\n❌ Tests échoués:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   • {result['test']}: {result['message']}")
        
        # Sauvegarder le rapport
        report_data = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": passed_tests/total_tests*100,
                "total_duration": total_duration,
                "timestamp": datetime.now().isoformat()
            },
            "results": self.test_results
        }
        
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Rapport sauvegardé: {report_file}")
        print("=" * 54)

def main():
    """Fonction principale"""
    test_suite = TestSuite()
    
    # Vérifier si le serveur est démarré
    try:
        requests.get("http://localhost:8000/api/health", timeout=2)
        print("✅ Serveur détecté sur localhost:8000")
    except:
        print("⚠️  Serveur non détecté sur localhost:8000")
        print("💡 Démarrez l'application avec: python enhanced_app.py")
        print("   Ou utilisez: python start_enhanced.py")
        
        response = input("\n🤔 Continuer les tests hors-ligne? (y/N): ")
        if response.lower() != 'y':
            return
    
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
