#!/usr/bin/env python3
"""
Application web BingX Trading - Version avec données réelles
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, Depends, HTTPException, status, WebSocket, WebSocketDisconnect, Request
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>RequestForm, HTTPBearer, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

# Ajouter le répertoire parent au path pour importer les modules BingX
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

# Import des modules BingX
from config import BingXConfig
from trading_bot import TradingBot

# Configuration
SECRET_KEY = "your-secret-key-change-this-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialisation de l'application FastAPI
app = FastAPI(
    title="BingX Trading Web App - Données Réelles",
    description="Interface web pour le trading sur BingX avec données réelles",
    version="1.0.0"
)

# Configuration des templates et fichiers statiques
current_dir = Path(__file__).parent
templates = Jinja2Templates(directory=str(current_dir / "templates"))
app.mount("/static", StaticFiles(directory=str(current_dir / "static")), name="static")

# Configuration de sécurité
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# Initialisation du bot BingX
config = BingXConfig.from_env()
trading_bot = TradingBot(config)

# Modèles Pydantic
class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    username: str

class OrderRequest(BaseModel):
    symbol: str
    side: str  # BUY ou SELL
    order_type: str  # MARKET ou LIMIT
    quantity: float
    price: Optional[float] = None

# Fonctions d'authentification
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def authenticate_user(username: str, password: str):
    if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
        return {"username": username}
    return False

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return User(username=username)

# Gestionnaire de connexions WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

# Routes principales
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

# Routes d'authentification
@app.post("/api/auth/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Routes de l'API avec données réelles BingX
@app.get("/api/account/balance")
async def get_account_balance(current_user: User = Depends(get_current_user)):
    try:
        balance = trading_bot.get_account_balance()
        return balance
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du solde: {e}")
        raise HTTPException(status_code=500, detail="Erreur lors de la récupération du solde")

@app.get("/api/account/positions")
async def get_positions(current_user: User = Depends(get_current_user)):
    try:
        positions = trading_bot.get_positions()
        return {"positions": positions}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des positions: {e}")
        raise HTTPException(status_code=500, detail="Erreur lors de la récupération des positions")

@app.get("/api/market/{symbol}")
async def get_market_data(symbol: str, current_user: User = Depends(get_current_user)):
    try:
        market_data = trading_bot.get_market_data(symbol)
        return market_data
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des données de marché: {e}")
        raise HTTPException(status_code=500, detail="Erreur lors de la récupération des données de marché")

@app.post("/api/trading/order")
async def place_order(order: OrderRequest, current_user: User = Depends(get_current_user)):
    try:
        if order.order_type.upper() == "MARKET":
            result = trading_bot.place_market_order(order.symbol, order.side, order.quantity)
        elif order.order_type.upper() == "LIMIT":
            if order.price is None:
                raise HTTPException(status_code=400, detail="Prix requis pour un ordre limite")
            result = trading_bot.place_limit_order(order.symbol, order.side, order.quantity, order.price)
        else:
            raise HTTPException(status_code=400, detail="Type d'ordre non supporté")
        
        # Notifier via WebSocket
        await manager.broadcast(json.dumps({
            "type": "order_placed",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }))
        
        return result
    except Exception as e:
        logger.error(f"Erreur lors du placement de l'ordre: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors du placement de l'ordre: {str(e)}")

# WebSocket pour les données en temps réel
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    logger.info("WebSocket connecté")
    
    try:
        while True:
            # Envoyer des données de marché en temps réel
            try:
                # Récupérer les données BTC-USDT par défaut
                market_data = trading_bot.get_market_data("BTC-USDT")
                balance = trading_bot.get_account_balance()
                
                data = {
                    "type": "market_update",
                    "market_data": market_data,
                    "balance": balance,
                    "timestamp": datetime.now().isoformat()
                }
                
                await manager.send_personal_message(json.dumps(data), websocket)
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi des données WebSocket: {e}")
            
            await asyncio.sleep(5)  # Mise à jour toutes les 5 secondes
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("WebSocket déconnecté")

if __name__ == "__main__":
    print("🚀 Application Web BingX Trading (Données Réelles)")
    print("=" * 60)
    print("🌐 URL: http://0.0.0.0:8000")
    print("👤 Identifiants: admin / admin123")
    print("💰 Données réelles de votre compte BingX")
    print("=" * 60)
    
    # Test de la connexion BingX au démarrage
    try:
        if trading_bot.initialize():
            print("✅ Connexion BingX réussie")
        else:
            print("❌ Échec de la connexion BingX")
    except Exception as e:
        print(f"❌ Erreur de connexion BingX: {e}")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
