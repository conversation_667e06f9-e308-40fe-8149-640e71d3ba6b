#!/usr/bin/env python3
"""
Application web BingX Trading - Version simplifiée
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAP<PERSON>, Depends, HTTPException, status, WebSocket, WebSocketDisconnect, Request
from fastapi.security import OAuth2P<PERSON><PERSON>RequestForm, HTTPBearer, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

# Configuration
SECRET_KEY = "your-secret-key-change-this-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialisation de l'application FastAPI
app = FastAPI(
    title="BingX Trading Web App",
    description="Interface web moderne pour le trading sur BingX",
    version="1.0.0"
)

# Configuration des templates et fichiers statiques
current_dir = Path(__file__).parent
templates = Jinja2Templates(directory=str(current_dir / "templates"))
app.mount("/static", StaticFiles(directory=str(current_dir / "static")), name="static")

# Configuration de sécurité
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# Base de données utilisateurs simple
fake_users_db = {
    ADMIN_USERNAME: {
        "username": ADMIN_USERNAME,
        "hashed_password": pwd_context.hash(ADMIN_PASSWORD),
        "is_active": True,
    }
}

# Modèles Pydantic
class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    username: str
    is_active: bool = True

class OrderRequest(BaseModel):
    symbol: str
    side: str
    order_type: str
    quantity: float
    price: Optional[float] = None

# Données simulées pour les tests
MOCK_ACCOUNT_DATA = {
    "code": 0,
    "msg": "",
    "data": {
        "balance": {
            "userId": "*********",
            "asset": "USDT",
            "balance": "1000.00",
            "equity": "950.00",
            "unrealizedProfit": "-50.00",
            "realisedProfit": "25.00",
            "availableMargin": "800.00",
            "usedMargin": "150.00",
            "freezedMargin": "0.00"
        }
    }
}

MOCK_POSITIONS = [
    {
        "symbol": "BTC-USDT",
        "positionAmt": "0.001",
        "entryPrice": "45000.00",
        "unrealizedProfit": "-5.00"
    },
    {
        "symbol": "ETH-USDT", 
        "positionAmt": "0.1",
        "entryPrice": "3000.00",
        "unrealizedProfit": "10.00"
    }
]

MOCK_MARKET_DATA = {
    "ticker": {
        "code": 0,
        "data": {
            "symbol": "BTC-USDT",
            "lastPrice": "44500.00",
            "priceChange": "-500.00",
            "priceChangePercent": "-1.11",
            "volume": "1000.00"
        }
    },
    "orderbook": {
        "code": 0,
        "data": {
            "bids": [["44500.00", "1.0"], ["44499.00", "2.0"]],
            "asks": [["44501.00", "1.5"], ["44502.00", "2.5"]]
        }
    }
}

# Fonctions d'authentification
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_user(username: str):
    if username in fake_users_db:
        user_dict = fake_users_db[username]
        return user_dict
    return None

def authenticate_user(username: str, password: str):
    user = get_user(username)
    if not user or not verify_password(password, user["hashed_password"]):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = get_user(username=username)
    if user is None:
        raise credentials_exception
    return User(username=user["username"], is_active=user["is_active"])

# Gestionnaire WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

# Routes d'authentification
@app.post("/api/auth/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Routes de l'API
@app.get("/api/account/balance")
async def get_account_balance(current_user: User = Depends(get_current_user)):
    return MOCK_ACCOUNT_DATA

@app.get("/api/account/positions")
async def get_positions(current_user: User = Depends(get_current_user)):
    return {"positions": MOCK_POSITIONS}

@app.get("/api/market/{symbol}")
async def get_market_data(symbol: str, current_user: User = Depends(get_current_user)):
    return MOCK_MARKET_DATA

@app.post("/api/trading/order")
async def place_order(order: OrderRequest, current_user: User = Depends(get_current_user)):
    # Simulation du placement d'ordre
    result = {
        "orderId": "12345",
        "symbol": order.symbol,
        "side": order.side,
        "type": order.order_type,
        "quantity": order.quantity,
        "status": "FILLED",
        "timestamp": datetime.now().isoformat()
    }
    
    # Notifier via WebSocket
    await manager.broadcast(json.dumps({
        "type": "order_placed",
        "data": result,
        "timestamp": datetime.now().isoformat()
    }))
    
    return result

# Routes des pages
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

# WebSocket pour les données en temps réel
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Envoyer des données simulées périodiquement
            await manager.broadcast(json.dumps({
                "type": "market_data",
                "symbol": "BTC-USDT",
                "data": MOCK_MARKET_DATA,
                "timestamp": datetime.now().isoformat()
            }))
            
            await manager.broadcast(json.dumps({
                "type": "positions",
                "data": MOCK_POSITIONS,
                "timestamp": datetime.now().isoformat()
            }))
            
            await asyncio.sleep(5)  # Mise à jour toutes les 5 secondes
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

def main():
    """Fonction principale"""
    print("🚀 Application Web BingX Trading (Mode Démo)")
    print("=" * 60)
    print("🌐 URL: http://0.0.0.0:8000")
    print("👤 Identifiants: admin / admin123")
    print("⚠️  Mode démo avec données simulées")
    print("=" * 60)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

if __name__ == "__main__":
    main()
