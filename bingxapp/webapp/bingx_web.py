#!/usr/bin/env python3
"""
Application web BingX Trading - Version intégrée avec vraies données
"""

import asyncio
import hashlib
import hmac
import json
import logging
import os
import sys
import time
from contextlib import asynccontextmanager
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlencode

import requests
import uvicorn
from fastapi import FastAPI, Depends, HTTPException, status, WebSocket, WebSocketDisconnect, Request
from fastapi.security import OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

# Configuration BingX intégrée
class BingXConfig:
    """Configuration pour l'API BingX"""
    
    def __init__(self):
        self.api_key = "EJV71q7OSJVf8imsnXDIIf83p0ULisEF4DWTvPKZIcMsRBvxkfSI4Sq8RjfoGqCQKxbszBflM2baCHjm6b25w"
        self.secret_key = "Sm8OgsYz4m0zrTpbAkORRtLx7SV5zpCiC4iXbZ5gSkYU84e3wJ6qXnfnGaU8djXvHxgQMPY5eXTXaiujH3Xw"
        self.base_url = "https://open-api.bingx.com"
        self.timeout = 30

# Client BingX intégré
class BingXClient:
    """Client pour l'API BingX"""
    
    def __init__(self, config: BingXConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = config.timeout
        self.logger = logging.getLogger(__name__)
        
    def _generate_signature(self, query_string: str) -> str:
        """Génère la signature HMAC pour l'authentification"""
        return hmac.new(
            self.config.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_timestamp(self) -> int:
        """Retourne le timestamp actuel en millisecondes"""
        return int(time.time() * 1000)
    
    def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, 
                     signed: bool = False) -> Dict[str, any]:
        """Effectue une requête à l'API BingX"""
        
        if params is None:
            params = {}
            
        # Ajouter le timestamp pour les requêtes signées
        if signed:
            params['timestamp'] = self._get_timestamp()
            
        # Construire l'URL
        url = f"{self.config.base_url}{endpoint}"
        
        # Préparer les headers
        headers = {
            'X-BX-APIKEY': self.config.api_key,
        }
        
        # Signer la requête si nécessaire
        if signed:
            query_string = urlencode(sorted(params.items()))
            signature = self._generate_signature(query_string)
            params['signature'] = signature
            
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, headers=headers)
            elif method.upper() == 'POST':
                if signed:
                    headers['Content-Type'] = 'application/x-www-form-urlencoded'
                    response = self.session.post(url, data=params, headers=headers)
                else:
                    headers['Content-Type'] = 'application/json'
                    response = self.session.post(url, json=params, headers=headers)
            else:
                raise ValueError(f"Méthode HTTP non supportée: {method}")
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Erreur lors de la requête à {url}: {e}")
            raise
    
    def get_server_time(self) -> Dict[str, any]:
        """Récupère l'heure du serveur"""
        return self._make_request('GET', '/openApi/swap/v2/server/time')
    
    def get_account_info(self) -> Dict[str, any]:
        """Récupère les informations du compte"""
        return self._make_request('GET', '/openApi/swap/v2/user/balance', signed=True)
    
    def get_positions(self) -> Dict[str, any]:
        """Récupère les positions ouvertes"""
        return self._make_request('GET', '/openApi/swap/v2/user/positions', signed=True)
    
    def get_ticker(self, symbol: str) -> Dict[str, any]:
        """Récupère le ticker pour un symbole"""
        params = {'symbol': symbol}
        return self._make_request('GET', '/openApi/swap/v2/quote/ticker', params=params)
    
    def get_orderbook(self, symbol: str, limit: int = 20) -> Dict[str, any]:
        """Récupère le carnet d'ordres"""
        params = {'symbol': symbol, 'limit': limit}
        return self._make_request('GET', '/openApi/swap/v2/quote/depth', params=params)
    
    def place_order(self, symbol: str, side: str, order_type: str, quantity: float,
                   price: Optional[float] = None, **kwargs) -> Dict[str, any]:
        """Place un ordre"""
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': order_type.upper(),
            'quantity': str(quantity)
        }
        
        if price is not None:
            params['price'] = str(price)
            
        for key, value in kwargs.items():
            if value is not None:
                params[key] = str(value)
                
        return self._make_request('POST', '/openApi/swap/v2/trade/order', params, signed=True)
    
    def test_connectivity(self) -> bool:
        """Teste la connectivité avec l'API"""
        try:
            result = self.get_server_time()
            return 'data' in result and 'serverTime' in result.get('data', {})
        except Exception as e:
            self.logger.error(f"Test de connectivité échoué: {e}")
            return False

# Bot de trading intégré
class TradingBot:
    """Bot de trading pour BingX"""
    
    def __init__(self, config: BingXConfig):
        self.config = config
        self.client = BingXClient(config)
        self.logger = logging.getLogger(__name__)
        
    def initialize(self) -> bool:
        """Initialise le bot et vérifie la connectivité"""
        self.logger.info("Initialisation du bot de trading BingX...")
        
        if not self.client.test_connectivity():
            self.logger.error("Impossible de se connecter à l'API BingX")
            return False
            
        try:
            account_info = self.client.get_account_info()
            self.logger.info("Compte connecté avec succès")
            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification du compte: {e}")
            return False
    
    def get_account_balance(self) -> Dict[str, any]:
        """Récupère le solde du compte"""
        try:
            return self.client.get_account_info()
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du solde: {e}")
            return {}
    
    def get_positions(self) -> List[Dict[str, any]]:
        """Récupère les positions ouvertes"""
        try:
            positions = self.client.get_positions()
            return positions.get('data', [])
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des positions: {e}")
            return []
    
    def get_market_data(self, symbol: str) -> Dict[str, any]:
        """Récupère les données de marché pour un symbole"""
        try:
            ticker = self.client.get_ticker(symbol)
            orderbook = self.client.get_orderbook(symbol, limit=10)
            
            return {
                'ticker': ticker,
                'orderbook': orderbook,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des données de marché: {e}")
            return {}
    
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Dict[str, any]]:
        """Place un ordre au marché"""
        try:
            self.logger.info(f"Placement d'un ordre {side} de {quantity} {symbol}")
            
            result = self.client.place_order(
                symbol=symbol,
                side=side,
                order_type='MARKET',
                quantity=quantity
            )
            
            self.logger.info(f"Ordre placé avec succès: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Erreur lors du placement de l'ordre: {e}")
            return None
    
    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Optional[Dict[str, any]]:
        """Place un ordre à cours limité"""
        try:
            self.logger.info(f"Placement d'un ordre limite {side} de {quantity} {symbol} à {price}")
            
            result = self.client.place_order(
                symbol=symbol,
                side=side,
                order_type='LIMIT',
                quantity=quantity,
                price=price
            )
            
            self.logger.info(f"Ordre limite placé avec succès: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Erreur lors du placement de l'ordre limite: {e}")
            return None

# Instance globale du bot de trading
trading_bot: Optional[TradingBot] = None

# Fonctions d'initialisation et de nettoyage
async def initialize_trading_bot():
    """Initialise le bot de trading avec les vraies données BingX"""
    global trading_bot

    try:
        config = BingXConfig()
        trading_bot = TradingBot(config)

        if trading_bot.initialize():
            logger.info("✅ Bot de trading initialisé avec les vraies données BingX")
            return True
        else:
            logger.error("❌ Échec de l'initialisation du bot de trading")
            return False
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'initialisation du bot: {e}")
        return False

async def shutdown_bot():
    """Nettoyage du bot de trading"""
    global trading_bot
    if trading_bot:
        logger.info("🛑 Arrêt du bot de trading")
        trading_bot = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application"""
    # Démarrage
    logger.info("🚀 Démarrage de l'application web BingX Trading (vraies données)")
    await initialize_trading_bot()
    yield
    # Arrêt
    logger.info("🛑 Arrêt de l'application web BingX Trading")
    await shutdown_bot()

# Configuration de l'application web
SECRET_KEY = "your-secret-key-change-this-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialisation de l'application FastAPI
app = FastAPI(
    title="BingX Trading Web App - Real Data",
    description="Interface web avec vraies données BingX",
    version="1.0.0",
    lifespan=lifespan
)

# Configuration des templates et fichiers statiques
current_dir = Path(__file__).parent
templates = Jinja2Templates(directory=str(current_dir / "templates"))
app.mount("/static", StaticFiles(directory=str(current_dir / "static")), name="static")

# Configuration de sécurité
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# Base de données utilisateurs simple
fake_users_db = {
    ADMIN_USERNAME: {
        "username": ADMIN_USERNAME,
        "hashed_password": pwd_context.hash(ADMIN_PASSWORD),
        "is_active": True,
    }
}

# Modèles Pydantic
class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    username: str
    is_active: bool = True

class OrderRequest(BaseModel):
    symbol: str
    side: str
    order_type: str
    quantity: float
    price: Optional[float] = None

# Fonctions d'authentification
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_user(username: str):
    if username in fake_users_db:
        user_dict = fake_users_db[username]
        return user_dict
    return None

def authenticate_user(username: str, password: str):
    user = get_user(username)
    if not user or not verify_password(password, user["hashed_password"]):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = get_user(username=username)
    if user is None:
        raise credentials_exception
    return User(username=user["username"], is_active=user["is_active"])

# Initialisation du bot de trading
async def initialize_trading_bot():
    """Initialise le bot de trading avec les vraies données BingX"""
    global trading_bot

    try:
        config = BingXConfig()
        trading_bot = TradingBot(config)

        if trading_bot.initialize():
            logger.info("✅ Bot de trading initialisé avec les vraies données BingX")
            return True
        else:
            logger.error("❌ Échec de l'initialisation du bot de trading")
            return False
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'initialisation du bot: {e}")
        return False

# Gestionnaire WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)

        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

# Routes d'authentification
@app.post("/api/auth/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Routes de l'API avec vraies données BingX
@app.get("/api/account/balance")
async def get_account_balance(current_user: User = Depends(get_current_user)):
    """Récupère le vrai solde du compte BingX"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")

    try:
        balance = trading_bot.get_account_balance()
        logger.info(f"Solde récupéré: {balance}")
        return balance
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du solde: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/account/positions")
async def get_positions(current_user: User = Depends(get_current_user)):
    """Récupère les vraies positions ouvertes"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")

    try:
        positions = trading_bot.get_positions()
        logger.info(f"Positions récupérées: {len(positions)} positions")
        return {"positions": positions}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des positions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market/{symbol}")
async def get_market_data(symbol: str, current_user: User = Depends(get_current_user)):
    """Récupère les vraies données de marché"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")

    try:
        market_data = trading_bot.get_market_data(symbol)
        logger.info(f"Données de marché récupérées pour {symbol}")
        return market_data
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des données de marché: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trading/order")
async def place_order(order: OrderRequest, current_user: User = Depends(get_current_user)):
    """Place un vrai ordre sur BingX"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")

    try:
        logger.info(f"Placement d'ordre: {order.dict()}")

        if order.order_type.upper() == "MARKET":
            result = trading_bot.place_market_order(order.symbol, order.side, order.quantity)
        elif order.order_type.upper() == "LIMIT":
            if order.price is None:
                raise HTTPException(status_code=400, detail="Prix requis pour un ordre limite")
            result = trading_bot.place_limit_order(order.symbol, order.side, order.quantity, order.price)
        else:
            raise HTTPException(status_code=400, detail="Type d'ordre non supporté")

        if result:
            # Notifier via WebSocket
            await manager.broadcast(json.dumps({
                "type": "order_placed",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }))
            logger.info(f"Ordre placé avec succès: {result}")
            return result
        else:
            raise HTTPException(status_code=500, detail="Échec du placement de l'ordre")

    except Exception as e:
        logger.error(f"Erreur lors du placement de l'ordre: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Routes des pages
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("dashboard_tradingview.html", {"request": request})

@app.get("/dashboard-plotly", response_class=HTMLResponse)
async def dashboard_plotly(request: Request):
    return templates.TemplateResponse("dashboard_fixed.html", {"request": request})

@app.get("/dashboard-old", response_class=HTMLResponse)
async def dashboard_old(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/dashboard-fixed", response_class=HTMLResponse)
async def dashboard_fixed(request: Request):
    return templates.TemplateResponse("dashboard_fixed_final.html", {"request": request})

@app.get("/dashboard-ultra", response_class=HTMLResponse)
async def dashboard_ultra(request: Request):
    return templates.TemplateResponse("dashboard_ultra_robust.html", {"request": request})

@app.get("/dashboard-corrected", response_class=HTMLResponse)
async def dashboard_corrected(request: Request):
    return templates.TemplateResponse("dashboard_fixed_final.html", {"request": request})

@app.get("/dashboard-simple", response_class=HTMLResponse)
async def dashboard_simple(request: Request):
    return templates.TemplateResponse("dashboard_simple.html", {"request": request})

@app.get("/dashboard-no-chart", response_class=HTMLResponse)
async def dashboard_no_chart(request: Request):
    return templates.TemplateResponse("dashboard_no_chart.html", {"request": request})

# WebSocket pour les vraies données en temps réel
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            if trading_bot:
                try:
                    # Envoyer les vraies données de marché
                    market_data = trading_bot.get_market_data("BTC-USDT")
                    if market_data:
                        await manager.broadcast(json.dumps({
                            "type": "market_data",
                            "symbol": "BTC-USDT",
                            "data": market_data,
                            "timestamp": datetime.now().isoformat()
                        }))

                    # Envoyer les vraies positions
                    positions = trading_bot.get_positions()
                    await manager.broadcast(json.dumps({
                        "type": "positions",
                        "data": positions,
                        "timestamp": datetime.now().isoformat()
                    }))

                except Exception as e:
                    logger.error(f"Erreur WebSocket: {e}")

            await asyncio.sleep(5)  # Mise à jour toutes les 5 secondes

    except WebSocketDisconnect:
        manager.disconnect(websocket)

def main():
    """Fonction principale"""
    print("🚀 Application Web BingX Trading (Vraies Données) - Version Corrigée")
    print("=" * 70)
    print("🌐 URL: http://0.0.0.0:8001")
    print("👤 Identifiants: admin / admin123")
    print("📊 Connexion à votre vrai compte BingX")
    print("💰 Affichage de vos positions réelles")
    print("⚠️  ATTENTION: Trading réel activé!")
    print("🔧 Corrections appliquées: FastAPI lifespan, TradingView fixé")
    print("=" * 70)

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )

if __name__ == "__main__":
    main()
