/* Styles personnalisés pour l'application BingX Trading */

/* Variables CSS */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #2d2d2d;
    --accent-color: #f39c12;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f1c40f;
    --info-color: #3498db;
    --text-light: #ecf0f1;
    --text-muted: #95a5a6;
    --border-color: #444;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Classes utilitaires */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Styles pour les graphiques */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
}

/* Styles pour le carnet d'ordres */
.orderbook-container {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.orderbook-row {
    display: flex;
    justify-content: space-between;
    padding: 2px 5px;
    border-radius: 3px;
    margin: 1px 0;
}

.orderbook-row:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.bid-row {
    background: linear-gradient(90deg, transparent 0%, rgba(39, 174, 96, 0.1) 100%);
}

.ask-row {
    background: linear-gradient(90deg, transparent 0%, rgba(231, 76, 60, 0.1) 100%);
}

/* Styles pour les métriques */
.metric-card {
    transition: transform 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
    transition: color 0.3s ease;
}

.metric-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Styles pour les positions */
.position-row {
    transition: background-color 0.2s ease;
}

.position-row:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.position-pnl {
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

/* Styles pour les formulaires de trading */
.trading-form {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
    border: 1px solid var(--accent-color);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.trading-form .form-control {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    transition: all 0.3s ease;
}

.trading-form .form-control:focus {
    background-color: rgba(0, 0, 0, 0.5);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
}

/* Styles pour les boutons */
.btn-trading {
    background: linear-gradient(45deg, var(--accent-color), #e67e22);
    border: none;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-trading:hover {
    background: linear-gradient(45deg, #e67e22, var(--accent-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.btn-buy {
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    border: none;
}

.btn-buy:hover {
    background: linear-gradient(45deg, #2ecc71, var(--success-color));
}

.btn-sell {
    background: linear-gradient(45deg, var(--danger-color), #c0392b);
    border: none;
}

.btn-sell:hover {
    background: linear-gradient(45deg, #c0392b, var(--danger-color));
}

/* Styles pour les alertes */
.alert-custom {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-trading {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
    border-left: 4px solid var(--accent-color);
    color: var(--accent-color);
}

/* Styles pour les indicateurs de statut */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    position: relative;
}

.status-indicator.pulse::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    border: 2px solid currentColor;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

/* Styles pour les notifications */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    max-width: 400px;
}

.notification .alert {
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Styles pour la sidebar */
.sidebar {
    background: linear-gradient(180deg, var(--secondary-color) 0%, #1e1e1e 100%);
    border-right: 2px solid var(--accent-color);
}

.sidebar .nav-link {
    color: var(--text-light);
    padding: 15px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    background: linear-gradient(90deg, rgba(243, 156, 18, 0.1), transparent);
    color: var(--accent-color);
    border-left-color: var(--accent-color);
}

.sidebar .nav-link.active {
    background: linear-gradient(90deg, var(--accent-color), rgba(243, 156, 18, 0.3));
    color: var(--primary-color);
    border-left-color: #e67e22;
    font-weight: bold;
}

/* Styles pour les tableaux */
.table-trading {
    background-color: transparent;
}

.table-trading th {
    background-color: rgba(243, 156, 18, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table-trading td {
    border-color: var(--border-color);
    vertical-align: middle;
}

.table-trading tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Styles pour les prix */
.price-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.1rem;
}

.price-up {
    color: var(--success-color);
}

.price-down {
    color: var(--danger-color);
}

.price-neutral {
    color: var(--text-light);
}

/* Styles pour les badges */
.badge-trading {
    background-color: var(--accent-color);
    color: var(--primary-color);
    font-weight: bold;
}

.badge-profit {
    background-color: var(--success-color);
}

.badge-loss {
    background-color: var(--danger-color);
}

/* Gestionnaire de thèmes */
.theme-selector {
    margin-left: auto;
}

.theme-option {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.theme-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-option.active {
    background-color: var(--accent-color);
    color: white;
}

.theme-preview {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

/* Transitions pour les changements de thème */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

.card, .modal-content, .navbar {
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Styles spécifiques aux thèmes */
.theme-dark {
    background: var(--background-gradient);
}

.theme-light {
    background: var(--background-gradient);
}

.theme-light .card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-blue {
    background: var(--background-gradient);
}

.theme-blue .metric-card {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(30, 42, 58, 0.8));
}

.theme-green {
    background: var(--background-gradient);
    font-family: 'Courier New', monospace;
}

.theme-green .metric-card {
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.1), rgba(26, 46, 26, 0.8));
    border: 1px solid var(--accent-color);
}

.theme-green .card {
    border: 1px solid var(--accent-color);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
}

/* Styles responsives */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1040;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 300px;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .theme-selector {
        margin-left: 0;
        margin-top: 10px;
    }
}

/* Système de notifications amélioré */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    padding: 16px;
}

.notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.notification-header i {
    margin-right: 8px;
    font-size: 16px;
}

.notification-title {
    font-weight: 600;
    flex: 1;
    color: var(--text-light);
}

.notification-time {
    font-size: 12px;
    color: var(--text-muted);
    margin-right: 8px;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: var(--text-light);
}

.notification-message {
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.4;
}

.notification-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.notification-action {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--accent-color);
    animation: notification-progress linear;
    transform-origin: left;
}

@keyframes notification-progress {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Types de notifications */
.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-success .notification-header i {
    color: var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--danger-color);
}

.notification-error .notification-header i {
    color: var(--danger-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-warning .notification-header i {
    color: var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-info .notification-header i {
    color: var(--info-color);
}

/* Styles pour les modales */
.modal-content {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Styles pour le modal de trading */
#tradingModal .modal-content {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#tradingModal .modal-header {
    background: linear-gradient(135deg, var(--accent-color), rgba(243, 156, 18, 0.8));
    color: white;
    border-radius: 12px 12px 0 0;
}

#tradingModal .modal-header .btn-close {
    filter: brightness(0) invert(1);
}

/* Prix actuel */
.current-price-display {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.price-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

.price-change {
    font-size: 0.9rem;
    margin-left: 8px;
}

.price-change.positive {
    color: var(--success-color);
}

.price-change.negative {
    color: var(--danger-color);
}

/* Boutons Buy/Sell */
.btn-check:checked + .btn-outline-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-check:checked + .btn-outline-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* Boutons de pourcentage */
.percentage-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.percentage-buttons .btn {
    flex: 1;
    min-width: 45px;
    font-size: 0.8rem;
}

/* Résumé de l'ordre */
.order-summary {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.order-summary h6 {
    color: var(--accent-color);
    margin-bottom: 12px;
}

/* Styles spécifiques aux ordres */
#tradingModal.buy-order .modal-header {
    background: linear-gradient(135deg, var(--success-color), rgba(39, 174, 96, 0.8));
}

#tradingModal.sell-order .modal-header {
    background: linear-gradient(135deg, var(--danger-color), rgba(231, 76, 60, 0.8));
}

/* Alertes de validation */
.alert-sm {
    padding: 8px 12px;
    font-size: 0.875rem;
}

.alert-sm ul {
    padding-left: 20px;
}

/* Aperçu de l'ordre */
.order-preview {
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 12px;
}

.order-preview .table {
    margin-bottom: 0;
    color: var(--text-light);
}

.order-preview .table td {
    border-color: var(--border-color);
    padding: 4px 8px;
}

/* Animations pour le modal */
#tradingModal .modal-dialog {
    transition: transform 0.3s ease-out;
}

#tradingModal.show .modal-dialog {
    transform: scale(1);
}

/* Responsive pour le modal */
@media (max-width: 768px) {
    #tradingModal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .percentage-buttons .btn {
        font-size: 0.7rem;
        padding: 4px 6px;
    }

    .current-price-display {
        padding: 8px;
    }

    .price-value {
        font-size: 1.2rem;
    }
}

/* Styles pour le dashboard enhanced */
.metric-card {
    background: linear-gradient(135deg, var(--secondary-color), rgba(45, 45, 45, 0.8));
    border: 1px solid var(--border-color);
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.metric-card .card-body {
    display: flex;
    align-items: center;
    padding: 20px;
}

.metric-icon {
    font-size: 2.5rem;
    margin-right: 16px;
    opacity: 0.8;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 4px;
}

.metric-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--danger-color);
}

/* Statut de connexion */
.connection-status {
    padding: 16px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-text {
    margin-left: 8px;
    color: var(--text-muted);
}

/* Actions rapides */
.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-actions .btn {
    border-radius: 20px;
    padding: 6px 16px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Contrôles de graphique */
.chart-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-controls .form-select {
    min-width: 120px;
}

/* Interface de trading */
.trading-interface {
    text-align: center;
}

.trading-shortcuts {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.trading-shortcuts h6 {
    color: var(--text-muted);
    margin-bottom: 16px;
}

.trading-shortcuts .row {
    gap: 8px;
}

/* Carnet d'ordres */
#orderBook {
    max-height: 400px;
    overflow-y: auto;
}

.order-book-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 2px;
    font-size: 0.85rem;
    font-family: 'Courier New', monospace;
}

.order-book-item.bid {
    background: rgba(39, 174, 96, 0.1);
    border-left: 3px solid var(--success-color);
}

.order-book-item.ask {
    background: rgba(231, 76, 60, 0.1);
    border-left: 3px solid var(--danger-color);
}

/* Tables améliorées */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    background: var(--secondary-color);
    border-color: var(--border-color);
    color: var(--text-muted);
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    border-color: var(--border-color);
    color: var(--text-light);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Métriques de performance */
.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-item .metric-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.metric-item .metric-value {
    color: var(--text-light);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Rapport de performance */
.performance-report {
    font-size: 0.9rem;
    line-height: 1.4;
}

.performance-report p {
    margin-bottom: 8px;
}

.performance-report strong {
    color: var(--accent-color);
}

/* Sections de contenu */
.content-section {
    animation: fadeIn 0.3s ease-in-out;
}

/* Responsive pour dashboard enhanced */
@media (max-width: 768px) {
    .metric-card .card-body {
        padding: 16px;
    }

    .metric-icon {
        font-size: 2rem;
        margin-right: 12px;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .quick-actions {
        flex-direction: column;
        gap: 4px;
    }

    .chart-controls {
        flex-direction: column;
        gap: 4px;
        align-items: stretch;
    }

    .trading-shortcuts .row {
        flex-direction: column;
        gap: 4px;
    }

    .connection-status {
        padding: 12px;
    }
}

/* Styles pour les alertes de prix */
.alert-form {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: 8px;
    padding: 16px;
}

.alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.alert-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(2px);
}

.alert-content {
    flex: 1;
}

.alert-symbol {
    font-weight: bold;
    color: var(--accent-color);
    font-size: 1.1rem;
}

.alert-condition {
    color: var(--text-light);
    margin: 4px 0;
    font-size: 0.95rem;
}

.alert-message {
    color: var(--text-muted);
    font-size: 0.85rem;
    margin: 4px 0;
}

.alert-meta {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.alert-actions {
    display: flex;
    gap: 4px;
}

.alert-actions .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Historique des alertes */
.history-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 6px;
    border-left: 3px solid var(--success-color);
}

.history-content {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Badge d'alertes */
.alerts-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Bouton d'alertes dans la navbar */
.alerts-button {
    position: relative;
    margin-left: 8px;
}

/* Animation pour les alertes déclenchées */
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.alert-triggered {
    animation: alertPulse 0.5s ease-in-out 3;
    border-color: var(--warning-color) !important;
    background: rgba(241, 196, 15, 0.1) !important;
}

/* Responsive pour les alertes */
@media (max-width: 768px) {
    .alert-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .alert-actions {
        align-self: flex-end;
    }

    .alert-form .row {
        gap: 8px;
    }

    .alert-form .col-md-3,
    .alert-form .col-md-2 {
        margin-bottom: 8px;
    }
}

/* Styles pour les tooltips */
.tooltip-inner {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--border-color);
}

/* Styles pour les spinners */
.spinner-trading {
    color: var(--accent-color);
}

/* Styles pour les progress bars */
.progress {
    background-color: rgba(0, 0, 0, 0.3);
}

.progress-bar-trading {
    background: linear-gradient(90deg, var(--accent-color), #e67e22);
}

/* Styles pour les dropdowns */
.dropdown-menu {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.dropdown-item {
    color: var(--text-light);
}

.dropdown-item:hover {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--accent-color);
}
