/**
 * Modal de trading interactif et amélioré
 */

class TradingModal {
    constructor() {
        this.modal = null;
        this.currentSymbol = 'BTC-USDT';
        this.orderType = 'MARKET';
        this.side = 'BUY';
        this.isOpen = false;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.createModal();
        this.setupValidation();
    }
    
    createModal() {
        const modalHTML = `
            <div class="modal fade" id="tradingModal" tabindex="-1" aria-labelledby="tradingModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="tradingModalLabel">
                                <i class="fas fa-exchange-alt"></i> Placer un Ordre
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- Sélection du symbole -->
                                <div class="col-md-6 mb-3">
                                    <label for="symbolSelect" class="form-label">Symbole</label>
                                    <select class="form-select" id="symbolSelect">
                                        <option value="BTC-USDT">BTC-USDT</option>
                                        <option value="ETH-USDT">ETH-USDT</option>
                                        <option value="BNB-USDT">BNB-USDT</option>
                                        <option value="ADA-USDT">ADA-USDT</option>
                                        <option value="SOL-USDT">SOL-USDT</option>
                                    </select>
                                </div>
                                
                                <!-- Prix actuel -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Prix Actuel</label>
                                    <div class="current-price-display">
                                        <span id="currentPrice" class="price-value">--</span>
                                        <span id="priceChange" class="price-change">--</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Boutons Buy/Sell -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="orderSide" id="buyRadio" value="BUY" checked>
                                        <label class="btn btn-outline-success" for="buyRadio">
                                            <i class="fas fa-arrow-up"></i> ACHETER
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="orderSide" id="sellRadio" value="SELL">
                                        <label class="btn btn-outline-danger" for="sellRadio">
                                            <i class="fas fa-arrow-down"></i> VENDRE
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Type d'ordre -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="orderType" id="marketRadio" value="MARKET" checked>
                                        <label class="btn btn-outline-primary" for="marketRadio">
                                            <i class="fas fa-bolt"></i> MARCHÉ
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="orderType" id="limitRadio" value="LIMIT">
                                        <label class="btn btn-outline-primary" for="limitRadio">
                                            <i class="fas fa-target"></i> LIMITE
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Prix limite (affiché seulement pour les ordres limite) -->
                            <div class="row mb-3" id="limitPriceRow" style="display: none;">
                                <div class="col-12">
                                    <label for="limitPrice" class="form-label">Prix Limite</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="limitPrice" step="0.01" placeholder="0.00">
                                        <span class="input-group-text">USDT</span>
                                    </div>
                                    <div class="form-text">Prix auquel vous souhaitez exécuter l'ordre</div>
                                </div>
                            </div>
                            
                            <!-- Quantité -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="quantity" class="form-label">Quantité</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="quantity" step="0.0001" placeholder="0.0000">
                                        <span class="input-group-text" id="quantityUnit">BTC</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Pourcentage</label>
                                    <div class="percentage-buttons">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-percentage="25">25%</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-percentage="50">50%</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-percentage="75">75%</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-percentage="100">100%</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Stop Loss et Take Profit -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="stopLoss" class="form-label">
                                        Stop Loss <span class="text-muted">(optionnel)</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="stopLoss" step="0.01" placeholder="0.00">
                                        <span class="input-group-text">USDT</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="takeProfit" class="form-label">
                                        Take Profit <span class="text-muted">(optionnel)</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="takeProfit" step="0.01" placeholder="0.00">
                                        <span class="input-group-text">USDT</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Résumé de l'ordre -->
                            <div class="order-summary">
                                <h6><i class="fas fa-calculator"></i> Résumé de l'Ordre</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Coût Total Estimé:</small>
                                        <div class="fw-bold" id="totalCost">-- USDT</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Frais Estimés:</small>
                                        <div class="fw-bold" id="estimatedFees">-- USDT</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Alertes de validation -->
                            <div id="validationAlerts"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Annuler
                            </button>
                            <button type="button" class="btn btn-primary" id="previewOrderBtn">
                                <i class="fas fa-eye"></i> Aperçu
                            </button>
                            <button type="button" class="btn btn-success" id="placeOrderBtn" disabled>
                                <i class="fas fa-check"></i> Placer l'Ordre
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Ajouter le modal au DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = new bootstrap.Modal(document.getElementById('tradingModal'));
    }
    
    setupEventListeners() {
        const modalElement = document.getElementById('tradingModal');
        
        // Changement de symbole
        document.getElementById('symbolSelect').addEventListener('change', (e) => {
            this.currentSymbol = e.target.value;
            this.updateSymbolInfo();
            this.updateQuantityUnit();
            this.calculateTotal();
        });
        
        // Changement de côté (Buy/Sell)
        document.querySelectorAll('input[name="orderSide"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.side = e.target.value;
                this.updateOrderSummary();
            });
        });
        
        // Changement de type d'ordre
        document.querySelectorAll('input[name="orderType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.orderType = e.target.value;
                this.toggleLimitPrice();
                this.calculateTotal();
            });
        });
        
        // Boutons de pourcentage
        document.querySelectorAll('[data-percentage]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const percentage = parseInt(e.target.dataset.percentage);
                this.setQuantityByPercentage(percentage);
            });
        });
        
        // Validation en temps réel
        ['quantity', 'limitPrice', 'stopLoss', 'takeProfit'].forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('input', () => {
                    this.validateForm();
                    this.calculateTotal();
                });
            }
        });
        
        // Boutons d'action
        document.getElementById('previewOrderBtn').addEventListener('click', () => {
            this.showOrderPreview();
        });
        
        document.getElementById('placeOrderBtn').addEventListener('click', () => {
            this.placeOrder();
        });
        
        // Mise à jour des prix en temps réel
        stateManager.subscribe('market.prices', (prices) => {
            if (prices[this.currentSymbol]) {
                this.updateCurrentPrice(prices[this.currentSymbol]);
            }
        });
    }
    
    setupValidation() {
        this.validationRules = {
            quantity: {
                required: true,
                min: 0.0001,
                message: 'La quantité doit être supérieure à 0.0001'
            },
            limitPrice: {
                required: false, // Seulement pour les ordres limite
                min: 0.01,
                message: 'Le prix limite doit être supérieur à 0.01'
            }
        };
    }
    
    open(symbol = 'BTC-USDT', side = 'BUY') {
        this.currentSymbol = symbol;
        this.side = side;
        
        // Mettre à jour l'interface
        document.getElementById('symbolSelect').value = symbol;
        document.querySelector(`input[value="${side}"]`).checked = true;
        
        this.updateSymbolInfo();
        this.updateQuantityUnit();
        this.modal.show();
        this.isOpen = true;
    }
    
    close() {
        this.modal.hide();
        this.isOpen = false;
        this.resetForm();
    }
    
    updateSymbolInfo() {
        // Mettre à jour les informations du symbole
        const price = stateManager.getState(`market.prices.${this.currentSymbol}`);
        if (price) {
            this.updateCurrentPrice(price);
        }
    }
    
    updateCurrentPrice(price) {
        document.getElementById('currentPrice').textContent = `${price.toFixed(2)} USDT`;
        // Ajouter la variation de prix si disponible
        // document.getElementById('priceChange').textContent = `****%`; // À implémenter
    }
    
    updateQuantityUnit() {
        const baseAsset = this.currentSymbol.split('-')[0];
        document.getElementById('quantityUnit').textContent = baseAsset;
    }
    
    toggleLimitPrice() {
        const limitPriceRow = document.getElementById('limitPriceRow');
        if (this.orderType === 'LIMIT') {
            limitPriceRow.style.display = 'block';
            this.validationRules.limitPrice.required = true;
        } else {
            limitPriceRow.style.display = 'none';
            this.validationRules.limitPrice.required = false;
        }
    }
    
    setQuantityByPercentage(percentage) {
        // Calculer la quantité basée sur le solde disponible
        const balance = stateManager.getState('account.balance');
        if (balance && balance.USDT) {
            const availableBalance = balance.USDT.available;
            const currentPrice = stateManager.getState(`market.prices.${this.currentSymbol}`) || 0;
            
            if (currentPrice > 0) {
                const maxQuantity = availableBalance / currentPrice;
                const quantity = (maxQuantity * percentage / 100).toFixed(4);
                document.getElementById('quantity').value = quantity;
                this.calculateTotal();
            }
        }
    }
    
    calculateTotal() {
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const price = this.orderType === 'LIMIT' 
            ? parseFloat(document.getElementById('limitPrice').value) || 0
            : stateManager.getState(`market.prices.${this.currentSymbol}`) || 0;
        
        const total = quantity * price;
        const fees = total * 0.001; // 0.1% de frais estimés
        
        document.getElementById('totalCost').textContent = `${total.toFixed(2)} USDT`;
        document.getElementById('estimatedFees').textContent = `${fees.toFixed(4)} USDT`;
    }
    
    validateForm() {
        const alerts = document.getElementById('validationAlerts');
        alerts.innerHTML = '';
        
        let isValid = true;
        const errors = [];
        
        // Valider la quantité
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        if (quantity < this.validationRules.quantity.min) {
            errors.push(this.validationRules.quantity.message);
            isValid = false;
        }
        
        // Valider le prix limite si nécessaire
        if (this.orderType === 'LIMIT') {
            const limitPrice = parseFloat(document.getElementById('limitPrice').value) || 0;
            if (limitPrice < this.validationRules.limitPrice.min) {
                errors.push(this.validationRules.limitPrice.message);
                isValid = false;
            }
        }
        
        // Afficher les erreurs
        if (errors.length > 0) {
            const alertHTML = `
                <div class="alert alert-warning alert-sm">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul class="mb-0">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
            alerts.innerHTML = alertHTML;
        }
        
        // Activer/désactiver le bouton
        document.getElementById('placeOrderBtn').disabled = !isValid;
        
        return isValid;
    }
    
    showOrderPreview() {
        const orderData = this.getOrderData();
        
        const previewHTML = `
            <div class="order-preview">
                <h6>Aperçu de l'Ordre</h6>
                <table class="table table-sm">
                    <tr><td>Symbole:</td><td>${orderData.symbol}</td></tr>
                    <tr><td>Côté:</td><td class="${orderData.side === 'BUY' ? 'text-success' : 'text-danger'}">${orderData.side}</td></tr>
                    <tr><td>Type:</td><td>${orderData.order_type}</td></tr>
                    <tr><td>Quantité:</td><td>${orderData.quantity}</td></tr>
                    ${orderData.price ? `<tr><td>Prix:</td><td>${orderData.price} USDT</td></tr>` : ''}
                    ${orderData.stop_loss ? `<tr><td>Stop Loss:</td><td>${orderData.stop_loss} USDT</td></tr>` : ''}
                    ${orderData.take_profit ? `<tr><td>Take Profit:</td><td>${orderData.take_profit} USDT</td></tr>` : ''}
                </table>
            </div>
        `;
        
        // Afficher dans une notification
        notificationManager.show({
            type: 'info',
            title: 'Aperçu de l\'ordre',
            message: previewHTML,
            duration: 10000,
            actions: [
                {
                    label: 'Confirmer',
                    callback: 'tradingModal.placeOrder()'
                }
            ]
        });
    }
    
    async placeOrder() {
        if (!this.validateForm()) {
            return;
        }
        
        const orderData = this.getOrderData();
        
        try {
            // Afficher un loader
            const placeBtn = document.getElementById('placeOrderBtn');
            const originalText = placeBtn.innerHTML;
            placeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Placement...';
            placeBtn.disabled = true;
            
            // Envoyer l'ordre
            const response = await fetch('/api/trading/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify(orderData)
            });
            
            if (response.ok) {
                const result = await response.json();
                
                notificationManager.showOrderSuccess({
                    id: result.orderId,
                    ...orderData
                });
                
                this.close();
            } else {
                const error = await response.json();
                notificationManager.showOrderError(error);
            }
            
        } catch (error) {
            notificationManager.showOrderError({ message: error.message });
        } finally {
            // Restaurer le bouton
            const placeBtn = document.getElementById('placeOrderBtn');
            placeBtn.innerHTML = originalText;
            placeBtn.disabled = false;
        }
    }
    
    getOrderData() {
        return {
            symbol: this.currentSymbol,
            side: this.side,
            order_type: this.orderType,
            quantity: parseFloat(document.getElementById('quantity').value),
            price: this.orderType === 'LIMIT' ? parseFloat(document.getElementById('limitPrice').value) : null,
            stop_loss: parseFloat(document.getElementById('stopLoss').value) || null,
            take_profit: parseFloat(document.getElementById('takeProfit').value) || null
        };
    }
    
    resetForm() {
        document.getElementById('quantity').value = '';
        document.getElementById('limitPrice').value = '';
        document.getElementById('stopLoss').value = '';
        document.getElementById('takeProfit').value = '';
        document.getElementById('validationAlerts').innerHTML = '';
        document.getElementById('totalCost').textContent = '-- USDT';
        document.getElementById('estimatedFees').textContent = '-- USDT';
    }
    
    updateOrderSummary() {
        // Mettre à jour l'apparence selon le côté
        const modal = document.getElementById('tradingModal');
        modal.classList.remove('buy-order', 'sell-order');
        modal.classList.add(this.side === 'BUY' ? 'buy-order' : 'sell-order');
    }
}

// Instance globale
const tradingModal = new TradingModal();

// Fonctions globales pour l'ouverture du modal
function openTradingModal(symbol = 'BTC-USDT', side = 'BUY') {
    tradingModal.open(symbol, side);
}

// Export pour utilisation dans d'autres modules
window.tradingModal = tradingModal;
window.openTradingModal = openTradingModal;
