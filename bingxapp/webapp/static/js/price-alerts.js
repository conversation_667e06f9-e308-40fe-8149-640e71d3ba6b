/**
 * Système d'alertes de prix pour l'application BingX Trading
 */

class PriceAlertManager {
    constructor() {
        this.alerts = new Map();
        this.activeAlerts = new Set();
        this.alertHistory = [];
        this.soundEnabled = localStorage.getItem('alertSound') !== 'false';
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        // Charger les alertes sauvegardées
        this.loadSavedAlerts();
        
        // Créer l'interface d'alertes
        this.createAlertsInterface();
        
        // Écouter les changements de prix
        stateManager.subscribe('market.prices', (prices) => {
            this.checkAlerts(prices);
        });
    }
    
    setupEventListeners() {
        // Écouter les événements de création d'alertes
        document.addEventListener('createPriceAlert', (event) => {
            this.createAlert(event.detail);
        });
    }
    
    createAlertsInterface() {
        // Créer le modal d'alertes
        const modalHTML = `
            <div class="modal fade" id="alertsModal" tabindex="-1" aria-labelledby="alertsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="alertsModalLabel">
                                <i class="fas fa-bell"></i> Gestion des Alertes de Prix
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Formulaire de création d'alerte -->
                            <div class="alert-form mb-4">
                                <h6><i class="fas fa-plus"></i> Créer une Nouvelle Alerte</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <select class="form-select" id="alertSymbol">
                                            <option value="BTC-USDT">BTC-USDT</option>
                                            <option value="ETH-USDT">ETH-USDT</option>
                                            <option value="BNB-USDT">BNB-USDT</option>
                                            <option value="ADA-USDT">ADA-USDT</option>
                                            <option value="SOL-USDT">SOL-USDT</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="alertCondition">
                                            <option value="above">Au-dessus</option>
                                            <option value="below">En-dessous</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" id="alertPrice" 
                                               placeholder="Prix cible" step="0.01">
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="alertType">
                                            <option value="once">Une fois</option>
                                            <option value="recurring">Récurrent</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-primary w-100" onclick="priceAlertManager.addAlert()">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <input type="text" class="form-control" id="alertMessage" 
                                               placeholder="Message personnalisé (optionnel)">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Liste des alertes actives -->
                            <div class="active-alerts">
                                <h6><i class="fas fa-list"></i> Alertes Actives</h6>
                                <div id="activeAlertsList">
                                    <!-- Alertes dynamiques -->
                                </div>
                            </div>
                            
                            <!-- Historique des alertes -->
                            <div class="alert-history mt-4">
                                <h6><i class="fas fa-history"></i> Historique des Alertes</h6>
                                <div id="alertHistoryList" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Historique dynamique -->
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="form-check me-auto">
                                <input class="form-check-input" type="checkbox" id="soundToggle" 
                                       ${this.soundEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="soundToggle">
                                    Son activé
                                </label>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            <button type="button" class="btn btn-danger" onclick="priceAlertManager.clearAllAlerts()">
                                Supprimer Tout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Configurer les événements
        document.getElementById('soundToggle').addEventListener('change', (e) => {
            this.soundEnabled = e.target.checked;
            localStorage.setItem('alertSound', this.soundEnabled);
        });
    }
    
    addAlert() {
        const symbol = document.getElementById('alertSymbol').value;
        const condition = document.getElementById('alertCondition').value;
        const price = parseFloat(document.getElementById('alertPrice').value);
        const type = document.getElementById('alertType').value;
        const message = document.getElementById('alertMessage').value;
        
        if (!price || price <= 0) {
            notificationManager.show({
                type: 'error',
                title: 'Erreur',
                message: 'Veuillez entrer un prix valide'
            });
            return;
        }
        
        const alert = {
            id: Date.now(),
            symbol,
            condition,
            price,
            type,
            message: message || `${symbol} ${condition === 'above' ? 'au-dessus de' : 'en-dessous de'} ${price}`,
            created: new Date(),
            triggered: false,
            enabled: true
        };
        
        this.alerts.set(alert.id, alert);
        this.activeAlerts.add(alert.id);
        
        // Sauvegarder
        this.saveAlerts();
        
        // Mettre à jour l'interface
        this.updateAlertsDisplay();
        
        // Réinitialiser le formulaire
        document.getElementById('alertPrice').value = '';
        document.getElementById('alertMessage').value = '';
        
        notificationManager.show({
            type: 'success',
            title: 'Alerte créée',
            message: `Alerte pour ${symbol} créée avec succès`
        });
    }
    
    createAlert(alertData) {
        const alert = {
            id: Date.now(),
            ...alertData,
            created: new Date(),
            triggered: false,
            enabled: true
        };
        
        this.alerts.set(alert.id, alert);
        this.activeAlerts.add(alert.id);
        this.saveAlerts();
        this.updateAlertsDisplay();
        
        return alert.id;
    }
    
    removeAlert(alertId) {
        this.alerts.delete(alertId);
        this.activeAlerts.delete(alertId);
        this.saveAlerts();
        this.updateAlertsDisplay();
    }
    
    toggleAlert(alertId) {
        const alert = this.alerts.get(alertId);
        if (alert) {
            alert.enabled = !alert.enabled;
            if (alert.enabled) {
                this.activeAlerts.add(alertId);
            } else {
                this.activeAlerts.delete(alertId);
            }
            this.saveAlerts();
            this.updateAlertsDisplay();
        }
    }
    
    checkAlerts(prices) {
        for (const alertId of this.activeAlerts) {
            const alert = this.alerts.get(alertId);
            if (!alert || !alert.enabled) continue;
            
            const currentPrice = prices[alert.symbol];
            if (!currentPrice) continue;
            
            let triggered = false;
            
            if (alert.condition === 'above' && currentPrice >= alert.price) {
                triggered = true;
            } else if (alert.condition === 'below' && currentPrice <= alert.price) {
                triggered = true;
            }
            
            if (triggered) {
                this.triggerAlert(alert, currentPrice);
            }
        }
    }
    
    triggerAlert(alert, currentPrice) {
        // Marquer comme déclenchée
        alert.triggered = true;
        alert.triggeredAt = new Date();
        alert.triggeredPrice = currentPrice;
        
        // Ajouter à l'historique
        this.alertHistory.unshift({
            ...alert,
            triggeredAt: alert.triggeredAt,
            triggeredPrice: currentPrice
        });
        
        // Limiter l'historique à 100 entrées
        if (this.alertHistory.length > 100) {
            this.alertHistory = this.alertHistory.slice(0, 100);
        }
        
        // Retirer des alertes actives si c'est une alerte unique
        if (alert.type === 'once') {
            this.activeAlerts.delete(alert.id);
        } else {
            // Pour les alertes récurrentes, réinitialiser
            alert.triggered = false;
        }
        
        // Notification visuelle
        notificationManager.showPriceAlert(alert.symbol, currentPrice, alert.condition);
        
        // Son d'alerte
        if (this.soundEnabled) {
            this.playAlertSound();
        }
        
        // Sauvegarder
        this.saveAlerts();
        this.updateAlertsDisplay();
        
        // Événement personnalisé
        document.dispatchEvent(new CustomEvent('priceAlertTriggered', {
            detail: { alert, currentPrice }
        }));
    }
    
    playAlertSound() {
        try {
            // Créer un son d'alerte simple
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.warn('Impossible de jouer le son d\'alerte:', error);
        }
    }
    
    updateAlertsDisplay() {
        this.updateActiveAlertsList();
        this.updateAlertHistory();
        this.updateAlertsBadge();
    }
    
    updateActiveAlertsList() {
        const container = document.getElementById('activeAlertsList');
        if (!container) return;
        
        const activeAlertsArray = Array.from(this.activeAlerts)
            .map(id => this.alerts.get(id))
            .filter(alert => alert && alert.enabled);
        
        if (activeAlertsArray.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucune alerte active</p>';
            return;
        }
        
        container.innerHTML = activeAlertsArray.map(alert => `
            <div class="alert-item" data-alert-id="${alert.id}">
                <div class="alert-content">
                    <div class="alert-symbol">${alert.symbol}</div>
                    <div class="alert-condition">
                        ${alert.condition === 'above' ? '↗️' : '↘️'} ${alert.price} USDT
                    </div>
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-meta">
                        Créée: ${alert.created.toLocaleString()}
                        ${alert.type === 'recurring' ? ' • Récurrente' : ''}
                    </div>
                </div>
                <div class="alert-actions">
                    <button class="btn btn-sm btn-outline-warning" 
                            onclick="priceAlertManager.toggleAlert(${alert.id})">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" 
                            onclick="priceAlertManager.removeAlert(${alert.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    updateAlertHistory() {
        const container = document.getElementById('alertHistoryList');
        if (!container) return;
        
        if (this.alertHistory.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucune alerte déclenchée</p>';
            return;
        }
        
        container.innerHTML = this.alertHistory.slice(0, 20).map(alert => `
            <div class="history-item">
                <div class="history-content">
                    <strong>${alert.symbol}</strong> 
                    ${alert.condition === 'above' ? 'au-dessus de' : 'en-dessous de'} 
                    ${alert.price} USDT
                    <br>
                    <small class="text-muted">
                        Déclenchée le ${alert.triggeredAt.toLocaleString()} 
                        à ${alert.triggeredPrice.toFixed(2)} USDT
                    </small>
                </div>
            </div>
        `).join('');
    }
    
    updateAlertsBadge() {
        const badge = document.querySelector('.alerts-badge');
        const count = this.activeAlerts.size;
        
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'inline';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    clearAllAlerts() {
        if (confirm('Êtes-vous sûr de vouloir supprimer toutes les alertes ?')) {
            this.alerts.clear();
            this.activeAlerts.clear();
            this.saveAlerts();
            this.updateAlertsDisplay();
            
            notificationManager.show({
                type: 'info',
                title: 'Alertes supprimées',
                message: 'Toutes les alertes ont été supprimées'
            });
        }
    }
    
    saveAlerts() {
        const alertsData = {
            alerts: Array.from(this.alerts.entries()),
            activeAlerts: Array.from(this.activeAlerts),
            history: this.alertHistory.slice(0, 50) // Sauvegarder seulement les 50 dernières
        };
        
        localStorage.setItem('priceAlerts', JSON.stringify(alertsData));
    }
    
    loadSavedAlerts() {
        try {
            const saved = localStorage.getItem('priceAlerts');
            if (saved) {
                const data = JSON.parse(saved);
                
                // Restaurer les alertes
                this.alerts = new Map(data.alerts || []);
                this.activeAlerts = new Set(data.activeAlerts || []);
                this.alertHistory = data.history || [];
                
                // Convertir les dates
                for (const alert of this.alerts.values()) {
                    alert.created = new Date(alert.created);
                    if (alert.triggeredAt) {
                        alert.triggeredAt = new Date(alert.triggeredAt);
                    }
                }
                
                this.alertHistory.forEach(item => {
                    item.created = new Date(item.created);
                    if (item.triggeredAt) {
                        item.triggeredAt = new Date(item.triggeredAt);
                    }
                });
            }
        } catch (error) {
            console.error('Erreur lors du chargement des alertes:', error);
        }
    }
    
    openAlertsModal() {
        const modal = new bootstrap.Modal(document.getElementById('alertsModal'));
        modal.show();
        this.updateAlertsDisplay();
    }
    
    getActiveAlertsCount() {
        return this.activeAlerts.size;
    }
    
    exportAlerts() {
        const data = {
            alerts: Array.from(this.alerts.entries()),
            history: this.alertHistory,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `price-alerts-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
}

// Instance globale
const priceAlertManager = new PriceAlertManager();

// Fonctions utilitaires globales
function openAlertsModal() {
    priceAlertManager.openAlertsModal();
}

function createQuickAlert(symbol, condition, price) {
    return priceAlertManager.createAlert({
        symbol,
        condition,
        price,
        type: 'once',
        message: `Alerte rapide pour ${symbol}`
    });
}

// Export pour utilisation dans d'autres modules
window.priceAlertManager = priceAlertManager;
window.openAlertsModal = openAlertsModal;
window.createQuickAlert = createQuickAlert;
