/**
 * Script pour désactiver temporairement le monitoring excessif
 * Exécutez ceci dans la console pour arrêter les alertes
 */

console.log('🔧 Désactivation du monitoring excessif...');

// Désactiver complètement le security monitor
if (window.securityMonitor) {
    // Sauvegarder les méthodes originales
    window.originalSecurityMethods = {
        logSecurityEvent: securityMonitor.logSecurityEvent,
        logSuspiciousActivity: securityMonitor.logSuspiciousActivity,
        checkForSuspiciousElements: securityMonitor.checkForSuspiciousElements,
        checkForInjectionAttempts: securityMonitor.checkForInjectionAttempts
    };
    
    // Remplacer par des fonctions vides
    securityMonitor.logSecurityEvent = function() {};
    securityMonitor.logSuspiciousActivity = function() {};
    securityMonitor.checkForSuspiciousElements = function() {};
    securityMonitor.checkForInjectionAttempts = function() {};
    
    // Vider les données existantes
    securityMonitor.securityEvents = [];
    securityMonitor.suspiciousActivities = [];
    
    console.log('✅ Security Monitor désactivé');
}

// Désactiver les logs excessifs du performance manager
if (window.performanceManager) {
    // Sauvegarder les méthodes originales
    window.originalPerformanceMethods = {
        recordError: performanceManager.recordError,
        logMetric: performanceManager.logMetric
    };
    
    // Remplacer par des versions silencieuses
    performanceManager.recordError = function(error) {
        // Enregistrer seulement les erreurs critiques
        if (error.type === 'critical') {
            this.metrics.errors.push(error);
        }
    };
    
    performanceManager.logMetric = function(name, value) {
        // Log seulement les métriques importantes
        if (name.includes('Critical') || name.includes('Fatal')) {
            console.log(`📊 ${name}:`, value);
        }
    };
    
    // Vider les erreurs existantes
    performanceManager.metrics.errors = [];
    
    console.log('✅ Performance Manager optimisé');
}

// Fonction pour réactiver le monitoring
window.enableMonitoring = function() {
    console.log('🔄 Réactivation du monitoring...');
    
    if (window.originalSecurityMethods && window.securityMonitor) {
        securityMonitor.logSecurityEvent = originalSecurityMethods.logSecurityEvent;
        securityMonitor.logSuspiciousActivity = originalSecurityMethods.logSuspiciousActivity;
        securityMonitor.checkForSuspiciousElements = originalSecurityMethods.checkForSuspiciousElements;
        securityMonitor.checkForInjectionAttempts = originalSecurityMethods.checkForInjectionAttempts;
        console.log('✅ Security Monitor réactivé');
    }
    
    if (window.originalPerformanceMethods && window.performanceManager) {
        performanceManager.recordError = originalPerformanceMethods.recordError;
        performanceManager.logMetric = originalPerformanceMethods.logMetric;
        console.log('✅ Performance Manager réactivé');
    }
};

// Afficher une notification de confirmation
if (window.notificationManager) {
    notificationManager.show({
        type: 'info',
        title: 'Monitoring désactivé',
        message: 'Les alertes excessives ont été désactivées. Utilisez enableMonitoring() pour les réactiver.',
        duration: 5000
    });
}

console.log('🎉 Monitoring excessif désactivé !');
console.log('💡 Pour réactiver : enableMonitoring()');
console.log('🔄 La page devrait maintenant être silencieuse');
