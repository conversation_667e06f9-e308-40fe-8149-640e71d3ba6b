/**
 * Système de monitoring de sécurité pour l'application BingX Trading
 */

class SecurityMonitor {
    constructor() {
        this.securityEvents = [];
        this.suspiciousActivities = [];
        this.loginAttempts = new Map();
        this.rateLimits = new Map();
        this.sessionData = {
            startTime: Date.now(),
            lastActivity: Date.now(),
            actionsCount: 0,
            ipAddress: null,
            userAgent: navigator.userAgent
        };
        
        this.securityRules = {
            maxLoginAttempts: 5,
            maxActionsPerMinute: 50, // Plus tolérant
            sessionTimeout: 60 * 60 * 1000, // 60 minutes
            suspiciousPatterns: [
                /<script[^>]*>.*<\/script>/i,
                /javascript:\s*[^;]/i,
                /eval\s*\(/i,
                /document\.cookie\s*=/i,
                /window\.location\s*=/i
            ]
        };
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.detectClientInfo();
        this.startSessionMonitoring();
        this.setupCSRFProtection();
        this.monitorDOMChanges();
    }
    
    detectClientInfo() {
        // Détecter l'adresse IP (approximative)
        fetch('https://api.ipify.org?format=json')
            .then(response => response.json())
            .then(data => {
                this.sessionData.ipAddress = data.ip;
                this.logSecurityEvent('session_start', {
                    ip: data.ip,
                    userAgent: this.sessionData.userAgent,
                    timestamp: new Date()
                });
            })
            .catch(() => {
                this.sessionData.ipAddress = 'unknown';
            });
        
        // Détecter les informations du navigateur
        this.sessionData.browserInfo = {
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }
    
    setupEventListeners() {
        // Surveiller les tentatives de connexion
        document.addEventListener('loginAttempt', (event) => {
            this.handleLoginAttempt(event.detail);
        });
        
        // Surveiller les actions utilisateur
        document.addEventListener('userAction', (event) => {
            this.handleUserAction(event.detail);
        });
        
        // Surveiller les erreurs JavaScript
        window.addEventListener('error', (event) => {
            this.handleJavaScriptError(event);
        });
        
        // Surveiller les tentatives d'injection
        document.addEventListener('input', (event) => {
            this.checkForInjectionAttempts(event.target.value);
        });
        
        // Surveiller l'activité de la session
        ['click', 'keypress', 'mousemove'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                this.updateLastActivity();
            });
        });
        
        // Surveiller les changements de visibilité
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }
    
    startSessionMonitoring() {
        // Vérifier la session toutes les minutes
        setInterval(() => {
            this.checkSessionSecurity();
        }, 60000);
        
        // Vérifier l'inactivité toutes les 30 secondes
        setInterval(() => {
            this.checkInactivity();
        }, 30000);
    }
    
    setupCSRFProtection() {
        // Générer un token CSRF
        this.csrfToken = this.generateCSRFToken();
        
        // Ajouter le token à toutes les requêtes
        const originalFetch = window.fetch;
        window.fetch = (url, options = {}) => {
            if (options.method && options.method !== 'GET') {
                options.headers = options.headers || {};
                options.headers['X-CSRF-Token'] = this.csrfToken;
            }
            return originalFetch(url, options);
        };
    }
    
    generateCSRFToken() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    monitorDOMChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.checkForSuspiciousElements(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    checkForSuspiciousElements(element) {
        // Vérifier les scripts injectés (mais ignorer les scripts légitimes)
        if (element.tagName === 'SCRIPT') {
            const src = element.src;
            const isLegitimate = !src ||
                                src.startsWith(window.location.origin) ||
                                src.includes('/static/js/') ||
                                src.includes('cdn.jsdelivr.net') ||
                                src.includes('unpkg.com') ||
                                src.includes('cdnjs.cloudflare.com');

            if (!isLegitimate) {
                this.logSuspiciousActivity('script_injection', {
                    element: element.outerHTML.substring(0, 200),
                    timestamp: new Date()
                });
            }
        }

        // Vérifier les iframes suspects
        if (element.tagName === 'IFRAME') {
            const src = element.src;
            if (src && !src.startsWith(window.location.origin)) {
                this.logSuspiciousActivity('external_iframe', {
                    src: src,
                    timestamp: new Date()
                });
            }
        }
    }
    
    handleLoginAttempt(details) {
        const { username, success, timestamp } = details;
        const key = `${username}_${this.sessionData.ipAddress}`;
        
        if (!this.loginAttempts.has(key)) {
            this.loginAttempts.set(key, []);
        }
        
        const attempts = this.loginAttempts.get(key);
        attempts.push({ success, timestamp });
        
        // Garder seulement les tentatives des 15 dernières minutes
        const fifteenMinutesAgo = Date.now() - 15 * 60 * 1000;
        const recentAttempts = attempts.filter(attempt => attempt.timestamp > fifteenMinutesAgo);
        this.loginAttempts.set(key, recentAttempts);
        
        // Vérifier les tentatives échouées
        const failedAttempts = recentAttempts.filter(attempt => !attempt.success);
        if (failedAttempts.length >= this.securityRules.maxLoginAttempts) {
            this.logSuspiciousActivity('brute_force_attempt', {
                username,
                ip: this.sessionData.ipAddress,
                attempts: failedAttempts.length,
                timestamp: new Date()
            });
            
            this.triggerSecurityAlert('Tentatives de connexion suspectes détectées');
        }
        
        this.logSecurityEvent(success ? 'login_success' : 'login_failure', {
            username,
            ip: this.sessionData.ipAddress,
            timestamp: new Date()
        });
    }
    
    handleUserAction(details) {
        this.sessionData.actionsCount++;
        this.updateLastActivity();
        
        // Vérifier le rate limiting
        const now = Date.now();
        const oneMinuteAgo = now - 60000;
        
        if (!this.rateLimits.has('actions')) {
            this.rateLimits.set('actions', []);
        }
        
        const actions = this.rateLimits.get('actions');
        actions.push(now);
        
        // Nettoyer les anciennes actions
        const recentActions = actions.filter(time => time > oneMinuteAgo);
        this.rateLimits.set('actions', recentActions);
        
        // Vérifier si l'utilisateur dépasse la limite
        if (recentActions.length > this.securityRules.maxActionsPerMinute) {
            this.logSuspiciousActivity('rate_limit_exceeded', {
                actionsPerMinute: recentActions.length,
                timestamp: new Date()
            });
            
            this.triggerSecurityAlert('Activité suspecte: trop d\'actions par minute');
        }
        
        this.logSecurityEvent('user_action', {
            action: details.action,
            timestamp: new Date()
        });
    }
    
    handleJavaScriptError(event) {
        // Analyser l'erreur pour détecter des tentatives d'exploitation
        const errorMessage = event.message.toLowerCase();
        const suspiciousKeywords = ['eval', 'script', 'injection', 'xss', 'csrf'];
        
        const isSuspicious = suspiciousKeywords.some(keyword => 
            errorMessage.includes(keyword)
        );
        
        if (isSuspicious) {
            this.logSuspiciousActivity('suspicious_js_error', {
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                timestamp: new Date()
            });
        }
        
        this.logSecurityEvent('js_error', {
            message: event.message,
            filename: event.filename,
            line: event.lineno,
            timestamp: new Date()
        });
    }
    
    checkForInjectionAttempts(input) {
        if (typeof input !== 'string' || input.length < 10) return;

        // Ignorer les inputs légitimes (comme les noms de fichiers JS)
        const isLegitimateInput = input.includes('.js') ||
                                 input.includes('/static/') ||
                                 input.includes('cdn.') ||
                                 input.length < 50;

        if (isLegitimateInput) return;

        const isSuspicious = this.securityRules.suspiciousPatterns.some(pattern =>
            pattern.test(input)
        );

        if (isSuspicious) {
            this.logSuspiciousActivity('injection_attempt', {
                input: input.substring(0, 100), // Limiter la taille
                timestamp: new Date()
            });

            this.triggerSecurityAlert('Tentative d\'injection détectée');
        }
    }
    
    updateLastActivity() {
        this.sessionData.lastActivity = Date.now();
    }
    
    checkInactivity() {
        const now = Date.now();
        const inactiveTime = now - this.sessionData.lastActivity;
        
        if (inactiveTime > this.securityRules.sessionTimeout) {
            this.logSecurityEvent('session_timeout', {
                inactiveTime: inactiveTime,
                timestamp: new Date()
            });
            
            this.triggerSessionTimeout();
        }
    }
    
    checkSessionSecurity() {
        const now = Date.now();
        const sessionDuration = now - this.sessionData.startTime;

        // Vérifier la durée de session (max 8 heures)
        if (sessionDuration > 8 * 60 * 60 * 1000) {
            this.logSecurityEvent('long_session', {
                duration: sessionDuration,
                timestamp: new Date()
            });

            this.triggerSecurityAlert('Session très longue détectée');
        }

        // Vérifier les activités suspectes récentes (seuil plus élevé)
        const recentSuspicious = this.suspiciousActivities.filter(
            activity => (now - activity.timestamp) < 10 * 60 * 1000 // 10 minutes
        );

        // Seuil plus élevé pour éviter les faux positifs
        if (recentSuspicious.length > 10) {
            this.triggerSecurityAlert('Activités suspectes multiples détectées');
        }
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            this.logSecurityEvent('tab_hidden', {
                timestamp: new Date()
            });
        } else {
            this.logSecurityEvent('tab_visible', {
                timestamp: new Date()
            });
            this.updateLastActivity();
        }
    }
    
    logSecurityEvent(type, details) {
        const event = {
            id: Date.now(),
            type,
            details,
            sessionId: this.sessionData.startTime,
            timestamp: new Date()
        };
        
        this.securityEvents.push(event);
        
        // Limiter à 1000 événements
        if (this.securityEvents.length > 1000) {
            this.securityEvents = this.securityEvents.slice(-500);
        }
        
        // Envoyer au serveur si critique
        if (this.isCriticalEvent(type)) {
            this.sendSecurityEventToServer(event);
        }
        
        console.log('Security Event:', event);
    }
    
    logSuspiciousActivity(type, details) {
        const activity = {
            id: Date.now(),
            type,
            details,
            sessionId: this.sessionData.startTime,
            timestamp: Date.now()
        };
        
        this.suspiciousActivities.push(activity);
        
        // Limiter à 100 activités suspectes
        if (this.suspiciousActivities.length > 100) {
            this.suspiciousActivities = this.suspiciousActivities.slice(-50);
        }
        
        console.warn('Suspicious Activity:', activity);
        
        // Toujours envoyer au serveur
        this.sendSecurityEventToServer({
            type: 'suspicious_activity',
            details: activity,
            timestamp: new Date()
        });
    }
    
    isCriticalEvent(type) {
        const criticalEvents = [
            'login_failure',
            'session_timeout',
            'suspicious_activity',
            'script_injection',
            'brute_force_attempt'
        ];
        
        return criticalEvents.includes(type);
    }
    
    sendSecurityEventToServer(event) {
        // Envoyer l'événement au serveur de manière asynchrone
        fetch('/api/security/event', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.csrfToken
            },
            body: JSON.stringify(event)
        }).catch(error => {
            console.error('Erreur envoi événement sécurité:', error);
        });
    }
    
    triggerSecurityAlert(message) {
        // Afficher une notification de sécurité
        if (window.notificationManager) {
            notificationManager.show({
                type: 'error',
                title: 'Alerte de Sécurité',
                message: message,
                persistent: true,
                actions: [
                    {
                        label: 'Voir détails',
                        callback: 'securityMonitor.showSecurityReport()'
                    }
                ]
            });
        }
        
        // Log critique
        console.error('SECURITY ALERT:', message);
    }
    
    triggerSessionTimeout() {
        // Déconnecter l'utilisateur
        if (window.notificationManager) {
            notificationManager.show({
                type: 'warning',
                title: 'Session expirée',
                message: 'Votre session a expiré pour des raisons de sécurité',
                persistent: true,
                actions: [
                    {
                        label: 'Se reconnecter',
                        callback: 'window.location.href = "/"'
                    }
                ]
            });
        }
        
        // Rediriger après 10 secondes
        setTimeout(() => {
            window.location.href = '/';
        }, 10000);
    }
    
    getSecurityReport() {
        return {
            session: this.sessionData,
            events: this.securityEvents.slice(-50), // 50 derniers événements
            suspiciousActivities: this.suspiciousActivities.slice(-20), // 20 dernières activités
            statistics: {
                totalEvents: this.securityEvents.length,
                totalSuspicious: this.suspiciousActivities.length,
                sessionDuration: Date.now() - this.sessionData.startTime,
                actionsCount: this.sessionData.actionsCount
            }
        };
    }
    
    showSecurityReport() {
        const report = this.getSecurityReport();
        
        // Créer un modal avec le rapport
        const modalHTML = `
            <div class="modal fade" id="securityReportModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-shield-alt"></i> Rapport de Sécurité
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="security-report">
                                <h6>Informations de Session</h6>
                                <p><strong>Durée:</strong> ${Math.floor(report.statistics.sessionDuration / 60000)} minutes</p>
                                <p><strong>Actions:</strong> ${report.statistics.actionsCount}</p>
                                <p><strong>IP:</strong> ${report.session.ipAddress}</p>
                                
                                <h6>Statistiques</h6>
                                <p><strong>Événements totaux:</strong> ${report.statistics.totalEvents}</p>
                                <p><strong>Activités suspectes:</strong> ${report.statistics.totalSuspicious}</p>
                                
                                <h6>Derniers Événements</h6>
                                <div style="max-height: 200px; overflow-y: auto;">
                                    ${report.events.slice(-10).map(event => `
                                        <div class="event-item">
                                            <small>${event.timestamp.toLocaleString()}</small>
                                            <strong>${event.type}</strong>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            <button type="button" class="btn btn-primary" onclick="securityMonitor.exportSecurityReport()">
                                Exporter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Supprimer le modal existant s'il y en a un
        const existingModal = document.getElementById('securityReportModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('securityReportModal'));
        modal.show();
    }
    
    exportSecurityReport() {
        const report = this.getSecurityReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    // Méthodes publiques pour déclencher des événements
    reportLoginAttempt(username, success) {
        document.dispatchEvent(new CustomEvent('loginAttempt', {
            detail: { username, success, timestamp: Date.now() }
        }));
    }
    
    reportUserAction(action) {
        document.dispatchEvent(new CustomEvent('userAction', {
            detail: { action, timestamp: Date.now() }
        }));
    }
}

// Instance globale
const securityMonitor = new SecurityMonitor();

// Export pour utilisation dans d'autres modules
window.securityMonitor = securityMonitor;
