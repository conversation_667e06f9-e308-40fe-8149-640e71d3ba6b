/**
 * Gestionnaire d'ordres avancés avec stop-loss et take-profit
 */

class AdvancedOrderManager {
    constructor() {
        this.orders = new Map();
        this.orderHistory = [];
        this.stopLossOrders = new Map();
        this.takeProfitOrders = new Map();
        this.trailingStops = new Map();
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        this.loadSavedOrders();
        this.createAdvancedOrderInterface();
        
        // Écouter les changements de prix pour les ordres conditionnels
        stateManager.subscribe('market.prices', (prices) => {
            this.checkConditionalOrders(prices);
        });
    }
    
    setupEventListeners() {
        // Écouter les ordres placés
        document.addEventListener('orderPlaced', (event) => {
            this.handleOrderPlaced(event.detail);
        });
        
        // Écouter les changements de positions
        stateManager.subscribe('account.positions', (positions) => {
            this.updatePositionOrders(positions);
        });
    }
    
    createAdvancedOrderInterface() {
        // Ajouter le bouton d'ordres avancés à la navbar
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const advancedOrdersBtn = document.createElement('li');
            advancedOrdersBtn.className = 'nav-item';
            advancedOrdersBtn.innerHTML = `
                <button class="btn btn-outline-light btn-sm" onclick="advancedOrderManager.openOrdersModal()">
                    <i class="fas fa-cogs"></i> Ordres Avancés
                    <span class="orders-badge" style="display: none;">0</span>
                </button>
            `;
            navbar.appendChild(advancedOrdersBtn);
        }
        
        // Créer le modal d'ordres avancés
        const modalHTML = `
            <div class="modal fade" id="advancedOrdersModal" tabindex="-1" aria-labelledby="advancedOrdersModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="advancedOrdersModalLabel">
                                <i class="fas fa-cogs"></i> Gestion des Ordres Avancés
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- Ordres actifs -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-list"></i> Ordres Actifs</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="activeOrdersList" style="max-height: 400px; overflow-y: auto;">
                                                <!-- Ordres dynamiques -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Gestion des positions -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-shield-alt"></i> Protection des Positions</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="positionProtectionList" style="max-height: 400px; overflow-y: auto;">
                                                <!-- Protection des positions -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Création d'ordres conditionnels -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-plus"></i> Créer un Ordre Conditionnel</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <select class="form-select" id="conditionalSymbol">
                                                        <option value="BTC-USDT">BTC-USDT</option>
                                                        <option value="ETH-USDT">ETH-USDT</option>
                                                        <option value="BNB-USDT">BNB-USDT</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <select class="form-select" id="conditionalType">
                                                        <option value="stop_loss">Stop Loss</option>
                                                        <option value="take_profit">Take Profit</option>
                                                        <option value="trailing_stop">Trailing Stop</option>
                                                        <option value="oco">OCO</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control" id="triggerPrice" 
                                                           placeholder="Prix déclencheur" step="0.01">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control" id="orderQuantity" 
                                                           placeholder="Quantité" step="0.0001">
                                                </div>
                                                <div class="col-md-2">
                                                    <input type="number" class="form-control" id="limitPrice" 
                                                           placeholder="Prix limite" step="0.01">
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn btn-primary w-100" onclick="advancedOrderManager.createConditionalOrder()">
                                                        Créer
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Historique des ordres -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-history"></i> Historique des Ordres</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="orderHistoryList" style="max-height: 300px; overflow-y: auto;">
                                                <!-- Historique dynamique -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            <button type="button" class="btn btn-warning" onclick="advancedOrderManager.cancelAllOrders()">
                                Annuler Tous
                            </button>
                            <button type="button" class="btn btn-info" onclick="advancedOrderManager.exportOrders()">
                                Exporter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    createConditionalOrder() {
        const symbol = document.getElementById('conditionalSymbol').value;
        const type = document.getElementById('conditionalType').value;
        const triggerPrice = parseFloat(document.getElementById('triggerPrice').value);
        const quantity = parseFloat(document.getElementById('orderQuantity').value);
        const limitPrice = parseFloat(document.getElementById('limitPrice').value);
        
        if (!triggerPrice || !quantity) {
            notificationManager.show({
                type: 'error',
                title: 'Erreur',
                message: 'Veuillez remplir tous les champs requis'
            });
            return;
        }
        
        const order = {
            id: Date.now(),
            symbol,
            type,
            triggerPrice,
            quantity,
            limitPrice: limitPrice || null,
            status: 'pending',
            created: new Date(),
            triggered: false
        };
        
        this.orders.set(order.id, order);
        
        // Ajouter à la map appropriée
        switch (type) {
            case 'stop_loss':
                this.stopLossOrders.set(order.id, order);
                break;
            case 'take_profit':
                this.takeProfitOrders.set(order.id, order);
                break;
            case 'trailing_stop':
                this.trailingStops.set(order.id, order);
                order.highestPrice = triggerPrice; // Pour trailing stop
                break;
        }
        
        this.saveOrders();
        this.updateOrdersDisplay();
        
        // Réinitialiser le formulaire
        document.getElementById('triggerPrice').value = '';
        document.getElementById('orderQuantity').value = '';
        document.getElementById('limitPrice').value = '';
        
        notificationManager.show({
            type: 'success',
            title: 'Ordre créé',
            message: `Ordre ${type} pour ${symbol} créé avec succès`
        });
    }
    
    checkConditionalOrders(prices) {
        for (const [orderId, order] of this.orders.entries()) {
            if (order.status !== 'pending') continue;
            
            const currentPrice = prices[order.symbol];
            if (!currentPrice) continue;
            
            let shouldTrigger = false;
            
            switch (order.type) {
                case 'stop_loss':
                    shouldTrigger = currentPrice <= order.triggerPrice;
                    break;
                case 'take_profit':
                    shouldTrigger = currentPrice >= order.triggerPrice;
                    break;
                case 'trailing_stop':
                    shouldTrigger = this.checkTrailingStop(order, currentPrice);
                    break;
            }
            
            if (shouldTrigger) {
                this.triggerOrder(order, currentPrice);
            }
        }
    }
    
    checkTrailingStop(order, currentPrice) {
        // Mettre à jour le prix le plus haut pour trailing stop
        if (currentPrice > order.highestPrice) {
            order.highestPrice = currentPrice;
            order.triggerPrice = currentPrice * 0.95; // 5% en dessous du plus haut
        }
        
        return currentPrice <= order.triggerPrice;
    }
    
    triggerOrder(order, currentPrice) {
        order.status = 'triggered';
        order.triggeredAt = new Date();
        order.triggeredPrice = currentPrice;
        
        // Simuler le placement de l'ordre
        this.executeOrder(order);
        
        // Ajouter à l'historique
        this.orderHistory.unshift({
            ...order,
            triggeredAt: order.triggeredAt,
            triggeredPrice: currentPrice
        });
        
        // Limiter l'historique
        if (this.orderHistory.length > 100) {
            this.orderHistory = this.orderHistory.slice(0, 100);
        }
        
        this.saveOrders();
        this.updateOrdersDisplay();
        
        notificationManager.show({
            type: 'warning',
            title: 'Ordre déclenché',
            message: `${order.type} pour ${order.symbol} déclenché à ${currentPrice.toFixed(2)} USDT`,
            actions: [
                {
                    label: 'Voir détails',
                    callback: `advancedOrderManager.showOrderDetails('${order.id}')`
                }
            ]
        });
    }
    
    executeOrder(order) {
        // Simuler l'exécution de l'ordre
        order.status = 'executed';
        order.executedAt = new Date();
        
        // Ici, vous pourriez appeler l'API réelle pour placer l'ordre
        console.log('Ordre exécuté:', order);
    }
    
    addStopLossToPosition(symbol, quantity, stopPrice) {
        const order = {
            id: Date.now(),
            symbol,
            type: 'stop_loss',
            triggerPrice: stopPrice,
            quantity,
            status: 'pending',
            created: new Date(),
            isPositionProtection: true
        };
        
        this.orders.set(order.id, order);
        this.stopLossOrders.set(order.id, order);
        this.saveOrders();
        this.updateOrdersDisplay();
        
        return order.id;
    }
    
    addTakeProfitToPosition(symbol, quantity, profitPrice) {
        const order = {
            id: Date.now(),
            symbol,
            type: 'take_profit',
            triggerPrice: profitPrice,
            quantity,
            status: 'pending',
            created: new Date(),
            isPositionProtection: true
        };
        
        this.orders.set(order.id, order);
        this.takeProfitOrders.set(order.id, order);
        this.saveOrders();
        this.updateOrdersDisplay();
        
        return order.id;
    }
    
    cancelOrder(orderId) {
        const order = this.orders.get(orderId);
        if (order) {
            order.status = 'cancelled';
            order.cancelledAt = new Date();
            
            // Retirer des maps actives
            this.stopLossOrders.delete(orderId);
            this.takeProfitOrders.delete(orderId);
            this.trailingStops.delete(orderId);
            
            this.saveOrders();
            this.updateOrdersDisplay();
            
            notificationManager.show({
                type: 'info',
                title: 'Ordre annulé',
                message: `Ordre ${order.type} pour ${order.symbol} annulé`
            });
        }
    }
    
    cancelAllOrders() {
        if (confirm('Êtes-vous sûr de vouloir annuler tous les ordres actifs ?')) {
            for (const [orderId, order] of this.orders.entries()) {
                if (order.status === 'pending') {
                    this.cancelOrder(orderId);
                }
            }
            
            notificationManager.show({
                type: 'warning',
                title: 'Ordres annulés',
                message: 'Tous les ordres actifs ont été annulés'
            });
        }
    }
    
    updateOrdersDisplay() {
        this.updateActiveOrdersList();
        this.updatePositionProtectionList();
        this.updateOrderHistory();
        this.updateOrdersBadge();
    }
    
    updateActiveOrdersList() {
        const container = document.getElementById('activeOrdersList');
        if (!container) return;
        
        const activeOrders = Array.from(this.orders.values())
            .filter(order => order.status === 'pending' && !order.isPositionProtection);
        
        if (activeOrders.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucun ordre actif</p>';
            return;
        }
        
        container.innerHTML = activeOrders.map(order => `
            <div class="order-item" data-order-id="${order.id}">
                <div class="order-content">
                    <div class="order-header">
                        <span class="order-symbol">${order.symbol}</span>
                        <span class="order-type badge bg-${this.getOrderTypeColor(order.type)}">${order.type}</span>
                    </div>
                    <div class="order-details">
                        Déclencheur: ${order.triggerPrice} USDT • Quantité: ${order.quantity}
                        ${order.limitPrice ? ` • Limite: ${order.limitPrice} USDT` : ''}
                    </div>
                    <div class="order-meta">
                        Créé: ${order.created.toLocaleString()}
                    </div>
                </div>
                <div class="order-actions">
                    <button class="btn btn-sm btn-outline-danger" onclick="advancedOrderManager.cancelOrder(${order.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    updatePositionProtectionList() {
        const container = document.getElementById('positionProtectionList');
        if (!container) return;
        
        const positions = stateManager.getState('account.positions') || [];
        
        if (positions.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucune position ouverte</p>';
            return;
        }
        
        container.innerHTML = positions.map(position => {
            const stopLoss = Array.from(this.stopLossOrders.values())
                .find(order => order.symbol === position.symbol && order.isPositionProtection);
            const takeProfit = Array.from(this.takeProfitOrders.values())
                .find(order => order.symbol === position.symbol && order.isPositionProtection);
            
            return `
                <div class="position-item">
                    <div class="position-header">
                        <span class="position-symbol">${position.symbol}</span>
                        <span class="position-side badge bg-${position.side === 'LONG' ? 'success' : 'danger'}">${position.side}</span>
                    </div>
                    <div class="position-details">
                        Taille: ${position.size} • Prix d'entrée: ${position.entryPrice}
                    </div>
                    <div class="protection-controls">
                        ${stopLoss ? 
                            `<span class="badge bg-warning">SL: ${stopLoss.triggerPrice}</span>` :
                            `<button class="btn btn-sm btn-outline-warning" onclick="advancedOrderManager.addQuickStopLoss('${position.symbol}', ${position.size})">+ Stop Loss</button>`
                        }
                        ${takeProfit ? 
                            `<span class="badge bg-success">TP: ${takeProfit.triggerPrice}</span>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="advancedOrderManager.addQuickTakeProfit('${position.symbol}', ${position.size})">+ Take Profit</button>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }
    
    updateOrderHistory() {
        const container = document.getElementById('orderHistoryList');
        if (!container) return;
        
        if (this.orderHistory.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucun historique</p>';
            return;
        }
        
        container.innerHTML = this.orderHistory.slice(0, 20).map(order => `
            <div class="history-item">
                <div class="history-content">
                    <strong>${order.symbol}</strong> ${order.type} 
                    ${order.status === 'executed' ? '✅' : order.status === 'cancelled' ? '❌' : '⏳'}
                    <br>
                    <small class="text-muted">
                        ${order.triggeredAt ? `Déclenché le ${order.triggeredAt.toLocaleString()}` : 
                          order.cancelledAt ? `Annulé le ${order.cancelledAt.toLocaleString()}` : 
                          `Créé le ${order.created.toLocaleString()}`}
                    </small>
                </div>
            </div>
        `).join('');
    }
    
    updateOrdersBadge() {
        const badge = document.querySelector('.orders-badge');
        const activeCount = Array.from(this.orders.values())
            .filter(order => order.status === 'pending').length;
        
        if (badge) {
            if (activeCount > 0) {
                badge.textContent = activeCount;
                badge.style.display = 'inline';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    getOrderTypeColor(type) {
        const colors = {
            'stop_loss': 'danger',
            'take_profit': 'success',
            'trailing_stop': 'warning',
            'oco': 'info'
        };
        return colors[type] || 'secondary';
    }
    
    addQuickStopLoss(symbol, quantity) {
        const currentPrice = stateManager.getState(`market.prices.${symbol}`);
        if (currentPrice) {
            const stopPrice = currentPrice * 0.95; // 5% en dessous
            this.addStopLossToPosition(symbol, quantity, stopPrice);
        }
    }
    
    addQuickTakeProfit(symbol, quantity) {
        const currentPrice = stateManager.getState(`market.prices.${symbol}`);
        if (currentPrice) {
            const profitPrice = currentPrice * 1.1; // 10% au-dessus
            this.addTakeProfitToPosition(symbol, quantity, profitPrice);
        }
    }
    
    openOrdersModal() {
        const modal = new bootstrap.Modal(document.getElementById('advancedOrdersModal'));
        modal.show();
        this.updateOrdersDisplay();
    }
    
    saveOrders() {
        const ordersData = {
            orders: Array.from(this.orders.entries()),
            history: this.orderHistory.slice(0, 50)
        };
        
        localStorage.setItem('advancedOrders', JSON.stringify(ordersData));
    }
    
    loadSavedOrders() {
        try {
            const saved = localStorage.getItem('advancedOrders');
            if (saved) {
                const data = JSON.parse(saved);
                
                this.orders = new Map(data.orders || []);
                this.orderHistory = data.history || [];
                
                // Reconstituer les maps spécialisées
                for (const order of this.orders.values()) {
                    order.created = new Date(order.created);
                    if (order.triggeredAt) order.triggeredAt = new Date(order.triggeredAt);
                    if (order.cancelledAt) order.cancelledAt = new Date(order.cancelledAt);
                    
                    if (order.status === 'pending') {
                        switch (order.type) {
                            case 'stop_loss':
                                this.stopLossOrders.set(order.id, order);
                                break;
                            case 'take_profit':
                                this.takeProfitOrders.set(order.id, order);
                                break;
                            case 'trailing_stop':
                                this.trailingStops.set(order.id, order);
                                break;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des ordres:', error);
        }
    }
    
    exportOrders() {
        const data = {
            orders: Array.from(this.orders.entries()),
            history: this.orderHistory,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `advanced-orders-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    showOrderDetails(orderId) {
        const order = this.orders.get(parseInt(orderId));
        if (order) {
            console.log('Détails de l\'ordre:', order);
            // Implémenter l'affichage des détails
        }
    }
}

// Instance globale
const advancedOrderManager = new AdvancedOrderManager();

// Export pour utilisation dans d'autres modules
window.advancedOrderManager = advancedOrderManager;
