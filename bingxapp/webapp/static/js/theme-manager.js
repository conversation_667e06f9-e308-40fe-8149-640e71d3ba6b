/**
 * Gestionnaire de thèmes pour l'application BingX Trading
 */

class ThemeManager {
    constructor() {
        this.themes = {
            dark: {
                name: 'Sombre',
                colors: {
                    '--primary-color': '#1a1a1a',
                    '--secondary-color': '#2d2d2d',
                    '--accent-color': '#f39c12',
                    '--success-color': '#27ae60',
                    '--danger-color': '#e74c3c',
                    '--warning-color': '#f1c40f',
                    '--info-color': '#3498db',
                    '--text-light': '#ecf0f1',
                    '--text-muted': '#95a5a6',
                    '--border-color': '#444',
                    '--background-gradient': 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
                }
            },
            light: {
                name: 'Clair',
                colors: {
                    '--primary-color': '#ffffff',
                    '--secondary-color': '#f8f9fa',
                    '--accent-color': '#007bff',
                    '--success-color': '#28a745',
                    '--danger-color': '#dc3545',
                    '--warning-color': '#ffc107',
                    '--info-color': '#17a2b8',
                    '--text-light': '#212529',
                    '--text-muted': '#6c757d',
                    '--border-color': '#dee2e6',
                    '--background-gradient': 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)'
                }
            },
            blue: {
                name: 'Bleu Trading',
                colors: {
                    '--primary-color': '#0d1421',
                    '--secondary-color': '#1e2a3a',
                    '--accent-color': '#00d4ff',
                    '--success-color': '#00ff88',
                    '--danger-color': '#ff4757',
                    '--warning-color': '#ffa502',
                    '--info-color': '#3742fa',
                    '--text-light': '#ffffff',
                    '--text-muted': '#8395a7',
                    '--border-color': '#2f3542',
                    '--background-gradient': 'linear-gradient(135deg, #0d1421 0%, #1e2a3a 100%)'
                }
            },
            green: {
                name: 'Vert Matrix',
                colors: {
                    '--primary-color': '#0a0e0a',
                    '--secondary-color': '#1a2e1a',
                    '--accent-color': '#00ff41',
                    '--success-color': '#39ff14',
                    '--danger-color': '#ff073a',
                    '--warning-color': '#ffff00',
                    '--info-color': '#00ffff',
                    '--text-light': '#00ff41',
                    '--text-muted': '#4a7c59',
                    '--border-color': '#2d5a2d',
                    '--background-gradient': 'linear-gradient(135deg, #0a0e0a 0%, #1a2e1a 100%)'
                }
            }
        };
        
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.init();
    }
    
    init() {
        this.applyTheme(this.currentTheme);
        this.createThemeSelector();
        this.setupEventListeners();
    }
    
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) {
            console.warn('Thème non trouvé:', themeName);
            return;
        }
        
        const root = document.documentElement;
        
        // Appliquer les couleurs du thème
        Object.entries(theme.colors).forEach(([property, value]) => {
            root.style.setProperty(property, value);
        });
        
        // Mettre à jour l'état
        this.currentTheme = themeName;
        localStorage.setItem('theme', themeName);
        stateManager.setState('ui.theme', themeName);
        
        // Ajouter une classe au body pour les styles spécifiques
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);
        
        // Notifier le changement
        this.notifyThemeChange(themeName);
        
        console.log('Thème appliqué:', theme.name);
    }
    
    createThemeSelector() {
        // Créer le sélecteur de thème dans la navbar
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;
        
        const themeSelector = document.createElement('div');
        themeSelector.className = 'theme-selector dropdown';
        themeSelector.innerHTML = `
            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" 
                    id="themeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-palette"></i> Thème
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="themeDropdown">
                ${Object.entries(this.themes).map(([key, theme]) => `
                    <li>
                        <a class="dropdown-item theme-option ${key === this.currentTheme ? 'active' : ''}" 
                           href="#" data-theme="${key}">
                            <i class="fas fa-circle theme-preview" style="color: ${theme.colors['--accent-color']}"></i>
                            ${theme.name}
                        </a>
                    </li>
                `).join('')}
            </ul>
        `;
        
        // Insérer dans la navbar
        const navbarNav = navbar.querySelector('.navbar-nav') || navbar.querySelector('.navbar-collapse');
        if (navbarNav) {
            navbarNav.appendChild(themeSelector);
        }
    }
    
    setupEventListeners() {
        // Écouter les clics sur les options de thème
        document.addEventListener('click', (event) => {
            if (event.target.closest('.theme-option')) {
                event.preventDefault();
                const themeName = event.target.closest('.theme-option').dataset.theme;
                this.changeTheme(themeName);
            }
        });
        
        // Raccourci clavier pour changer de thème
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === 't') {
                event.preventDefault();
                this.cycleTheme();
            }
        });
        
        // Détecter les préférences système
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                if (!localStorage.getItem('theme')) {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }
    
    changeTheme(themeName) {
        if (this.themes[themeName]) {
            this.applyTheme(themeName);
            this.updateThemeSelector();
            
            // Animation de transition
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
            
            // Notification
            notificationManager.show({
                type: 'info',
                title: 'Thème changé',
                message: `Thème "${this.themes[themeName].name}" appliqué`,
                duration: 2000
            });
        }
    }
    
    cycleTheme() {
        const themeNames = Object.keys(this.themes);
        const currentIndex = themeNames.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeNames.length;
        this.changeTheme(themeNames[nextIndex]);
    }
    
    updateThemeSelector() {
        const options = document.querySelectorAll('.theme-option');
        options.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === this.currentTheme);
        });
    }
    
    notifyThemeChange(themeName) {
        // Notifier les autres composants du changement de thème
        const event = new CustomEvent('themeChanged', {
            detail: { theme: themeName, colors: this.themes[themeName].colors }
        });
        document.dispatchEvent(event);
    }
    
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    getThemeColors(themeName = this.currentTheme) {
        return this.themes[themeName]?.colors || {};
    }
    
    addCustomTheme(name, colors) {
        this.themes[name] = {
            name: name,
            colors: colors
        };
        
        // Recréer le sélecteur
        const existingSelector = document.querySelector('.theme-selector');
        if (existingSelector) {
            existingSelector.remove();
            this.createThemeSelector();
        }
    }
    
    exportTheme(themeName = this.currentTheme) {
        const theme = this.themes[themeName];
        if (!theme) return;
        
        const themeData = {
            name: theme.name,
            colors: theme.colors,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(themeData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `theme-${themeName}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    importTheme(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const themeData = JSON.parse(e.target.result);
                const themeName = themeData.name.toLowerCase().replace(/\s+/g, '-');
                
                this.addCustomTheme(themeName, themeData.colors);
                
                notificationManager.show({
                    type: 'success',
                    title: 'Thème importé',
                    message: `Thème "${themeData.name}" ajouté avec succès`,
                    actions: [
                        {
                            label: 'Appliquer',
                            callback: `themeManager.changeTheme('${themeName}')`
                        }
                    ]
                });
                
            } catch (error) {
                notificationManager.show({
                    type: 'error',
                    title: 'Erreur d\'importation',
                    message: 'Fichier de thème invalide'
                });
            }
        };
        reader.readAsText(file);
    }
}

// Instance globale
const themeManager = new ThemeManager();

// Export pour utilisation dans d'autres modules
window.themeManager = themeManager;
