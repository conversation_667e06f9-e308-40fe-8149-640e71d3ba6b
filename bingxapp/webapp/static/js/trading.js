/**
 * Fonctions JavaScript pour l'interface de trading BingX - Version Améliorée
 */

// Configuration globale améliorée
const TRADING_CONFIG = {
    UPDATE_INTERVAL: 3000, // 3 secondes (optimisé)
    CHART_UPDATE_INTERVAL: 1000, // 1 seconde
    WEBSOCKET_RECONNECT_DELAY: 3000, // 3 secondes (optimisé)
    DEFAULT_SYMBOLS: ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT'],
    PRICE_PRECISION: 2,
    QUANTITY_PRECISION: 4,
    MAX_CACHE_SIZE: 1000,
    CACHE_TTL: 30000, // 30 secondes
    DEBOUNCE_DELAY: 300, // 300ms pour les inputs
    MAX_RECONNECT_ATTEMPTS: 5
};

// Gestionnaire d'état global amélioré
class StateManager {
    constructor() {
        this.state = {
            user: null,
            account: {
                balance: null,
                positions: [],
                orders: []
            },
            market: {
                symbols: new Map(),
                orderBooks: new Map(),
                prices: new Map()
            },
            ui: {
                activeSection: 'overview',
                theme: localStorage.getItem('theme') || 'dark',
                notifications: [],
                loading: false
            },
            websocket: {
                connected: false,
                reconnectAttempts: 0,
                lastHeartbeat: null
            }
        };

        this.subscribers = new Map();
        this.cache = new Map();
        this.setupPerformanceMonitoring();
    }

    // Système de cache intelligent
    setCache(key, value, ttl = TRADING_CONFIG.CACHE_TTL) {
        const expiry = Date.now() + ttl;
        this.cache.set(key, { value, expiry });

        // Nettoyage automatique du cache
        if (this.cache.size > TRADING_CONFIG.MAX_CACHE_SIZE) {
            this.cleanCache();
        }
    }

    getCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        if (Date.now() > cached.expiry) {
            this.cache.delete(key);
            return null;
        }

        return cached.value;
    }

    cleanCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now > value.expiry) {
                this.cache.delete(key);
            }
        }
    }

    // Monitoring des performances
    setupPerformanceMonitoring() {
        this.metrics = {
            updateCount: 0,
            lastUpdate: Date.now(),
            averageUpdateTime: 0,
            memoryUsage: 0
        };

        // Monitoring périodique
        setInterval(() => {
            this.updateMetrics();
        }, 10000); // Toutes les 10 secondes
    }

    updateMetrics() {
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        }

        // Log des métriques si nécessaire
        if (this.metrics.memoryUsage > 50) { // Plus de 50MB
            console.warn('Utilisation mémoire élevée:', this.metrics.memoryUsage.toFixed(2), 'MB');
        }
    }

    // Gestion d'état réactive
    setState(path, value) {
        const startTime = performance.now();

        this.setNestedProperty(this.state, path, value);
        this.notifySubscribers(path, value);

        // Mise à jour des métriques
        this.metrics.updateCount++;
        const updateTime = performance.now() - startTime;
        this.metrics.averageUpdateTime =
            (this.metrics.averageUpdateTime + updateTime) / 2;
    }

    getState(path) {
        return this.getNestedProperty(this.state, path);
    }

    subscribe(path, callback) {
        if (!this.subscribers.has(path)) {
            this.subscribers.set(path, new Set());
        }
        this.subscribers.get(path).add(callback);

        // Retourner une fonction de désabonnement
        return () => {
            const pathSubscribers = this.subscribers.get(path);
            if (pathSubscribers) {
                pathSubscribers.delete(callback);
            }
        };
    }

    notifySubscribers(path, value) {
        // Notifier les abonnés exacts
        const exactSubscribers = this.subscribers.get(path);
        if (exactSubscribers) {
            exactSubscribers.forEach(callback => {
                try {
                    callback(value, path);
                } catch (error) {
                    console.error('Erreur dans le callback subscriber:', error);
                }
            });
        }

        // Notifier les abonnés parents (ex: 'market' pour 'market.prices')
        const pathParts = path.split('.');
        for (let i = pathParts.length - 1; i > 0; i--) {
            const parentPath = pathParts.slice(0, i).join('.');
            const parentSubscribers = this.subscribers.get(parentPath);
            if (parentSubscribers) {
                parentSubscribers.forEach(callback => {
                    try {
                        callback(this.getState(parentPath), parentPath);
                    } catch (error) {
                        console.error('Erreur dans le callback subscriber parent:', error);
                    }
                });
            }
        }
    }

    setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }

        current[keys[keys.length - 1]] = value;
    }

    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    // Méthodes utilitaires pour l'état
    updateMarketData(symbol, data) {
        this.setState(`market.symbols.${symbol}`, data);
        this.setState(`market.prices.${symbol}`, data.price);
        this.setCache(`market_${symbol}`, data);
    }

    updateAccountBalance(balance) {
        this.setState('account.balance', balance);
        this.setCache('account_balance', balance);
    }

    updatePositions(positions) {
        this.setState('account.positions', positions);
        this.setCache('account_positions', positions);
    }

    addNotification(notification) {
        const notifications = this.getState('ui.notifications') || [];
        const newNotification = {
            id: Date.now(),
            timestamp: new Date(),
            ...notification
        };

        notifications.unshift(newNotification);

        // Limiter à 50 notifications
        if (notifications.length > 50) {
            notifications.splice(50);
        }

        this.setState('ui.notifications', notifications);

        // Auto-suppression après 5 secondes pour les notifications non persistantes
        if (!notification.persistent) {
            setTimeout(() => {
                this.removeNotification(newNotification.id);
            }, 5000);
        }
    }

    removeNotification(id) {
        const notifications = this.getState('ui.notifications') || [];
        const filtered = notifications.filter(n => n.id !== id);
        this.setState('ui.notifications', filtered);
    }

    setLoading(loading) {
        this.setState('ui.loading', loading);
    }

    setWebSocketStatus(connected) {
        this.setState('websocket.connected', connected);
        this.setState('websocket.lastHeartbeat', connected ? Date.now() : null);

        if (connected) {
            this.setState('websocket.reconnectAttempts', 0);
        }
    }

    incrementReconnectAttempts() {
        const attempts = this.getState('websocket.reconnectAttempts') || 0;
        this.setState('websocket.reconnectAttempts', attempts + 1);
        return attempts + 1;
    }
}

// Instance globale du gestionnaire d'état
const stateManager = new StateManager();

// Gestionnaire de données en temps réel amélioré
class RealTimeDataManager {
    constructor() {
        this.subscribers = new Map();
        this.lastData = new Map();
        this.websocket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = TRADING_CONFIG.MAX_RECONNECT_ATTEMPTS;
        this.messageQueue = [];
        this.heartbeatInterval = null;
        this.reconnectTimeout = null;
        this.rateLimiter = new Map();
        this.debouncedUpdates = new Map();

        this.connect();
        this.setupHeartbeat();
    }

    // Connexion WebSocket améliorée
    connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('✅ WebSocket connecté');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                stateManager.setWebSocketStatus(true);

                // Traiter la queue de messages en attente
                this.processMessageQueue();

                stateManager.addNotification({
                    type: 'success',
                    title: 'Connexion établie',
                    message: 'Données en temps réel activées'
                });
            };

            this.websocket.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.websocket.onclose = () => {
                console.log('❌ WebSocket déconnecté');
                this.handleDisconnection();
            };

            this.websocket.onerror = (error) => {
                console.error('Erreur WebSocket:', error);
                this.handleDisconnection();
            };

        } catch (error) {
            console.error('Erreur de connexion WebSocket:', error);
            this.scheduleReconnect();
        }
    }

    // Gestion des messages avec rate limiting
    handleMessage(data) {
        try {
            const message = JSON.parse(data);

            // Rate limiting
            if (this.isRateLimited(message.type)) {
                return;
            }

            // Debouncing pour certains types de messages
            if (message.type === 'market_data') {
                this.debounceUpdate(`market_${message.symbol}`, () => {
                    this.processMessage(message);
                });
            } else {
                this.processMessage(message);
            }

        } catch (error) {
            console.error('Erreur parsing message WebSocket:', error);
        }
    }

    processMessage(message) {
        // Mettre à jour le state manager
        switch (message.type) {
            case 'market_data':
                stateManager.updateMarketData(message.symbol, message.data);
                break;
            case 'positions':
                stateManager.updatePositions(message.data);
                break;
            case 'account_balance':
                stateManager.updateAccountBalance(message.data);
                break;
            case 'pong':
                // Réponse au ping
                break;
        }

        // Notifier les abonnés
        this.notify(message.type, message);
    }

    // Système de heartbeat
    setupHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.websocket) {
                try {
                    this.websocket.send(JSON.stringify({ type: 'ping' }));
                    stateManager.setState('websocket.lastHeartbeat', Date.now());
                } catch (error) {
                    console.warn('Erreur heartbeat:', error);
                    this.handleDisconnection();
                }
            }
        }, 30000);
    }

    // Rate limiting
    isRateLimited(type) {
        const now = Date.now();
        const lastCall = this.rateLimiter.get(type) || 0;
        const minInterval = type === 'market_data' ? 1000 : 500;

        if (now - lastCall < minInterval) {
            return true;
        }

        this.rateLimiter.set(type, now);
        return false;
    }

    // Debouncing
    debounceUpdate(key, callback, delay = TRADING_CONFIG.DEBOUNCE_DELAY) {
        if (this.debouncedUpdates.has(key)) {
            clearTimeout(this.debouncedUpdates.get(key));
        }

        const timeoutId = setTimeout(() => {
            callback();
            this.debouncedUpdates.delete(key);
        }, delay);

        this.debouncedUpdates.set(key, timeoutId);
    }

    // Gestion de la déconnexion
    handleDisconnection() {
        this.isConnected = false;
        stateManager.setWebSocketStatus(false);

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        this.scheduleReconnect();
    }

    // Reconnexion automatique
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            stateManager.addNotification({
                type: 'error',
                title: 'Connexion échouée',
                message: 'Impossible de se reconnecter. Rechargez la page.',
                persistent: true
            });
            return;
        }

        const attempts = stateManager.incrementReconnectAttempts();
        const delay = Math.min(1000 * Math.pow(2, attempts), 30000); // Backoff exponentiel

        stateManager.addNotification({
            type: 'warning',
            title: 'Reconnexion...',
            message: `Tentative ${attempts}/${this.maxReconnectAttempts} dans ${delay/1000}s`
        });

        this.reconnectTimeout = setTimeout(() => {
            this.connect();
        }, delay);
    }

    // Queue de messages pour la reconnexion
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            if (this.isConnected) {
                this.websocket.send(JSON.stringify(message));
            }
        }
    }

    // Méthodes publiques améliorées
    subscribe(dataType, callback) {
        if (!this.subscribers.has(dataType)) {
            this.subscribers.set(dataType, new Set());
        }
        this.subscribers.get(dataType).add(callback);

        // Retourner une fonction de désabonnement
        return () => this.unsubscribe(dataType, callback);
    }

    unsubscribe(dataType, callback) {
        if (this.subscribers.has(dataType)) {
            this.subscribers.get(dataType).delete(callback);
        }
    }

    notify(dataType, data) {
        this.lastData.set(dataType, data);

        if (this.subscribers.has(dataType)) {
            this.subscribers.get(dataType).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Erreur dans le callback:', error);
                }
            });
        }
    }

    getLastData(dataType) {
        return this.lastData.get(dataType);
    }

    // Envoyer un message via WebSocket
    send(message) {
        if (this.isConnected && this.websocket) {
            try {
                this.websocket.send(JSON.stringify(message));
            } catch (error) {
                console.error('Erreur envoi message:', error);
                this.messageQueue.push(message);
            }
        } else {
            this.messageQueue.push(message);
        }
    }

    // Nettoyage
    destroy() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }
        if (this.websocket) {
            this.websocket.close();
        }

        // Nettoyer les timeouts de debouncing
        this.debouncedUpdates.forEach(timeoutId => clearTimeout(timeoutId));
        this.debouncedUpdates.clear();
    }
}

// Instance globale du gestionnaire de données
const dataManager = new RealTimeDataManager();

// Gestionnaire de graphiques
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.priceHistory = new Map();
    }
    
    createPriceChart(containerId, symbol) {
        const container = document.getElementById(containerId);
        if (!container) return null;
        
        const layout = {
            title: `${symbol} - Prix en temps réel`,
            xaxis: { title: 'Temps' },
            yaxis: { title: 'Prix (USDT)' },
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#ecf0f1' },
            margin: { t: 50, r: 50, b: 50, l: 80 }
        };
        
        const trace = {
            x: [],
            y: [],
            type: 'scatter',
            mode: 'lines',
            name: symbol,
            line: { color: '#f39c12', width: 2 }
        };
        
        Plotly.newPlot(containerId, [trace], layout, {
            responsive: true,
            displayModeBar: false
        });
        
        this.charts.set(containerId, { symbol, trace });
        this.priceHistory.set(symbol, []);
        
        return containerId;
    }
    
    updatePriceChart(containerId, price) {
        const chart = this.charts.get(containerId);
        if (!chart) return;
        
        const now = new Date();
        const history = this.priceHistory.get(chart.symbol);
        
        // Ajouter le nouveau point
        history.push({ time: now, price: parseFloat(price) });
        
        // Garder seulement les 100 derniers points
        if (history.length > 100) {
            history.shift();
        }
        
        // Mettre à jour le graphique
        const x = history.map(point => point.time);
        const y = history.map(point => point.price);
        
        Plotly.restyle(containerId, {
            x: [x],
            y: [y]
        }, 0);
    }
    
    createVolumeChart(containerId, data) {
        // Implémentation du graphique de volume
        const layout = {
            title: 'Volume 24h',
            xaxis: { title: 'Temps' },
            yaxis: { title: 'Volume' },
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#ecf0f1' }
        };
        
        const trace = {
            x: data.times,
            y: data.volumes,
            type: 'bar',
            marker: { color: '#3498db' }
        };
        
        Plotly.newPlot(containerId, [trace], layout, {
            responsive: true,
            displayModeBar: false
        });
    }
}

// Instance globale du gestionnaire de graphiques
const chartManager = new ChartManager();

// Gestionnaire de notifications
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
        this.notifications = new Map();
        this.nextId = 1;
    }
    
    createContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification';
            document.body.appendChild(container);
        }
        return container;
    }
    
    show(message, type = 'info', duration = 5000) {
        const id = this.nextId++;
        const notification = this.createNotification(id, message, type);
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);
        
        // Animation d'entrée
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto-suppression
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }
        
        return id;
    }
    
    createNotification(id, message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="notificationManager.hide(${id})"></button>
        `;
        return notification;
    }
    
    hide(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 150);
        }
    }
    
    clear() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }
}

// Instance globale du gestionnaire de notifications
const notificationManager = new NotificationManager();

// Gestionnaire d'ordres
class OrderManager {
    constructor() {
        this.pendingOrders = new Map();
        this.orderHistory = [];
    }
    
    async placeOrder(orderData) {
        try {
            // Validation des données
            this.validateOrderData(orderData);
            
            // Confirmation utilisateur
            if (!await this.confirmOrder(orderData)) {
                return null;
            }
            
            // Envoi de l'ordre
            const response = await fetch('/api/trading/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify(orderData)
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || 'Erreur lors du placement de l\'ordre');
            }
            
            const result = await response.json();
            
            // Ajouter à l'historique
            this.orderHistory.unshift({
                ...orderData,
                ...result,
                timestamp: new Date(),
                status: 'placed'
            });
            
            notificationManager.show(
                `Ordre ${orderData.side} placé avec succès pour ${orderData.quantity} ${orderData.symbol}`,
                'success'
            );
            
            return result;
            
        } catch (error) {
            console.error('Erreur placement ordre:', error);
            notificationManager.show(error.message, 'danger');
            throw error;
        }
    }
    
    validateOrderData(orderData) {
        if (!orderData.symbol) throw new Error('Symbole requis');
        if (!orderData.side) throw new Error('Côté requis');
        if (!orderData.order_type) throw new Error('Type d\'ordre requis');
        if (!orderData.quantity || orderData.quantity <= 0) throw new Error('Quantité invalide');
        
        if (orderData.order_type === 'LIMIT' && (!orderData.price || orderData.price <= 0)) {
            throw new Error('Prix requis pour un ordre limite');
        }
    }
    
    async confirmOrder(orderData) {
        const message = `
            Confirmer l'ordre ${orderData.side} ?
            
            Symbole: ${orderData.symbol}
            Type: ${orderData.order_type}
            Quantité: ${orderData.quantity}
            ${orderData.price ? `Prix: ${orderData.price}` : ''}
        `;
        
        return confirm(message.trim());
    }
    
    getOrderHistory() {
        return [...this.orderHistory];
    }
}

// Instance globale du gestionnaire d'ordres
const orderManager = new OrderManager();

// Utilitaires de formatage
const formatUtils = {
    number: (num, decimals = 2) => {
        return parseFloat(num).toLocaleString('fr-FR', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    currency: (amount, currency = 'USDT') => {
        return `${formatUtils.number(amount)} ${currency}`;
    },
    
    percentage: (value, decimals = 2) => {
        return `${formatUtils.number(value, decimals)}%`;
    },
    
    time: (date) => {
        return new Date(date).toLocaleTimeString('fr-FR');
    },
    
    date: (date) => {
        return new Date(date).toLocaleDateString('fr-FR');
    },
    
    datetime: (date) => {
        return new Date(date).toLocaleString('fr-FR');
    }
};

// Utilitaires de calcul
const calcUtils = {
    priceChange: (current, previous) => {
        return ((current - previous) / previous) * 100;
    },
    
    spread: (bid, ask) => {
        return ((ask - bid) / bid) * 100;
    },
    
    pnl: (entryPrice, currentPrice, quantity, side) => {
        const multiplier = side === 'BUY' ? 1 : -1;
        return (currentPrice - entryPrice) * quantity * multiplier;
    },
    
    marginUsage: (usedMargin, totalBalance) => {
        return (usedMargin / totalBalance) * 100;
    }
};

// Gestionnaire de thème
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.applyTheme();
    }
    
    applyTheme() {
        document.body.setAttribute('data-theme', this.currentTheme);
    }
    
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.currentTheme);
        this.applyTheme();
    }
    
    setTheme(theme) {
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        this.applyTheme();
    }
}

// Instance globale du gestionnaire de thème
const themeManager = new ThemeManager();

// Gestionnaire de raccourcis clavier
class KeyboardManager {
    constructor() {
        this.shortcuts = new Map();
        this.init();
    }
    
    init() {
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyString(e);
            const handler = this.shortcuts.get(key);
            
            if (handler) {
                e.preventDefault();
                handler();
            }
        });
        
        // Raccourcis par défaut
        this.addShortcut('Ctrl+B', () => this.quickBuy());
        this.addShortcut('Ctrl+S', () => this.quickSell());
        this.addShortcut('Escape', () => this.closeModals());
    }
    
    addShortcut(keyString, handler) {
        this.shortcuts.set(keyString, handler);
    }
    
    removeShortcut(keyString) {
        this.shortcuts.delete(keyString);
    }
    
    getKeyString(event) {
        const parts = [];
        
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        
        parts.push(event.key);
        
        return parts.join('+');
    }
    
    quickBuy() {
        // Implémentation de l'achat rapide
        console.log('Achat rapide');
    }
    
    quickSell() {
        // Implémentation de la vente rapide
        console.log('Vente rapide');
    }
    
    closeModals() {
        // Fermer toutes les modales ouvertes
        document.querySelectorAll('.modal.show').forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) bsModal.hide();
        });
    }
}

// Instance globale du gestionnaire de raccourcis
const keyboardManager = new KeyboardManager();

// Initialisation globale
document.addEventListener('DOMContentLoaded', function() {
    console.log('Trading.js initialisé');
    
    // Abonner aux données en temps réel
    dataManager.subscribe('market_data', (data) => {
        if (data.symbol === 'BTC-USDT' && data.data.ticker) {
            const price = data.data.ticker.data.lastPrice;
            chartManager.updatePriceChart('priceChart', price);
        }
    });
    
    // Initialiser les graphiques si les conteneurs existent
    if (document.getElementById('priceChart')) {
        chartManager.createPriceChart('priceChart', 'BTC-USDT');
    }
});

// Exporter les utilitaires globalement
window.formatUtils = formatUtils;
window.calcUtils = calcUtils;
window.notificationManager = notificationManager;
window.orderManager = orderManager;
window.chartManager = chartManager;
window.dataManager = dataManager;
window.themeManager = themeManager;
