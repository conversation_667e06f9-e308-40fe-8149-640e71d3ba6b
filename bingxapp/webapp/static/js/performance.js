/**
 * Gestionnaire de performance et monitoring pour l'application BingX Trading
 */

class PerformanceManager {
    constructor() {
        this.metrics = {
            pageLoad: 0,
            apiCalls: new Map(),
            websocketMessages: 0,
            memoryUsage: 0,
            renderTime: 0,
            errors: []
        };
        
        this.observers = new Map();
        this.startTime = performance.now();
        
        this.init();
        this.setupMonitoring();
    }
    
    init() {
        // Mesurer le temps de chargement de la page
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.metrics.pageLoad = performance.now() - this.startTime;
                this.logMetric('Page Load Time', this.metrics.pageLoad);
            });
        } else {
            this.metrics.pageLoad = performance.now() - this.startTime;
        }
        
        // Observer les erreurs JavaScript
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error?.stack,
                timestamp: new Date()
            });
        });
        
        // Observer les erreurs de promesses non gérées
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: new Date()
            });
        });
    }
    
    setupMonitoring() {
        // Monitoring périodique
        setInterval(() => {
            this.updateMemoryUsage();
            this.checkPerformance();
        }, 10000); // Toutes les 10 secondes
        
        // Observer les mutations DOM pour détecter les re-renders excessifs
        if (window.MutationObserver) {
            this.setupDOMObserver();
        }
        
        // Observer les ressources réseau
        if (window.PerformanceObserver) {
            this.setupResourceObserver();
        }
    }
    
    setupDOMObserver() {
        const observer = new MutationObserver((mutations) => {
            const start = performance.now();
            // Compter les mutations importantes
            let significantMutations = 0;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    significantMutations++;
                }
            });
            
            if (significantMutations > 10) {
                console.warn('Trop de mutations DOM détectées:', significantMutations);
            }
            
            const renderTime = performance.now() - start;
            this.metrics.renderTime = (this.metrics.renderTime + renderTime) / 2; // Moyenne mobile
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: false
        });
        
        this.observers.set('dom', observer);
    }
    
    setupResourceObserver() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach(entry => {
                if (entry.entryType === 'navigation') {
                    this.logMetric('Navigation Timing', {
                        domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                        loadComplete: entry.loadEventEnd - entry.loadEventStart,
                        totalTime: entry.loadEventEnd - entry.fetchStart
                    });
                }
                
                if (entry.entryType === 'resource' && entry.name.includes('/api/')) {
                    this.recordApiCall(entry.name, entry.duration);
                }
            });
        });
        
        observer.observe({ entryTypes: ['navigation', 'resource'] });
        this.observers.set('resource', observer);
    }
    
    updateMemoryUsage() {
        if (performance.memory) {
            this.metrics.memoryUsage = {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) // MB
            };
            
            // Alerter si l'utilisation mémoire est élevée
            if (this.metrics.memoryUsage.used > 100) { // Plus de 100MB
                console.warn('Utilisation mémoire élevée:', this.metrics.memoryUsage);
                
                notificationManager.show({
                    type: 'warning',
                    title: 'Utilisation mémoire élevée',
                    message: `${this.metrics.memoryUsage.used}MB utilisés`,
                    duration: 5000
                });
            }
        }
    }
    
    checkPerformance() {
        // Vérifier les métriques de performance
        const issues = [];
        
        if (this.metrics.renderTime > 16) { // Plus de 16ms = moins de 60fps
            issues.push('Temps de rendu élevé');
        }
        
        if (this.metrics.websocketMessages > 1000) { // Trop de messages WebSocket
            issues.push('Trop de messages WebSocket');
        }
        
        const slowApiCalls = Array.from(this.metrics.apiCalls.entries())
            .filter(([url, data]) => data.averageTime > 2000); // Plus de 2 secondes
        
        if (slowApiCalls.length > 0) {
            issues.push(`${slowApiCalls.length} API(s) lente(s)`);
        }
        
        if (issues.length > 0) {
            console.warn('Problèmes de performance détectés:', issues);
        }
    }
    
    recordApiCall(url, duration) {
        if (!this.metrics.apiCalls.has(url)) {
            this.metrics.apiCalls.set(url, {
                count: 0,
                totalTime: 0,
                averageTime: 0,
                lastCall: null
            });
        }
        
        const apiData = this.metrics.apiCalls.get(url);
        apiData.count++;
        apiData.totalTime += duration;
        apiData.averageTime = apiData.totalTime / apiData.count;
        apiData.lastCall = new Date();
        
        // Alerter si l'API est lente
        if (duration > 5000) { // Plus de 5 secondes
            console.warn('API lente détectée:', url, duration + 'ms');
        }
    }
    
    recordWebSocketMessage() {
        this.metrics.websocketMessages++;
    }
    
    recordError(error) {
        // Ignorer les erreurs mineures ou répétitives
        const isMinorError = error.message && (
            error.message.includes('Script error') ||
            error.message.includes('Non-Error promise rejection') ||
            error.message.includes('ResizeObserver loop limit exceeded')
        );

        if (isMinorError) return;

        this.metrics.errors.push(error);

        // Limiter à 100 erreurs
        if (this.metrics.errors.length > 100) {
            this.metrics.errors = this.metrics.errors.slice(-50);
        }

        // Log seulement les erreurs importantes
        if (error.type === 'javascript' || error.type === 'promise') {
            console.warn('Erreur importante:', error.message);

            // Notifier seulement les erreurs critiques
            if (error.message && !error.message.includes('Loading')) {
                notificationManager.show({
                    type: 'error',
                    title: 'Erreur détectée',
                    message: error.message.substring(0, 100),
                    duration: 5000
                });
            }
        }
    }
    
    logMetric(name, value) {
        console.log(`📊 ${name}:`, value);
    }
    
    getMetrics() {
        return {
            ...this.metrics,
            uptime: performance.now() - this.startTime
        };
    }
    
    getPerformanceReport() {
        const metrics = this.getMetrics();
        
        return {
            summary: {
                pageLoadTime: metrics.pageLoad,
                uptime: metrics.uptime,
                memoryUsage: metrics.memoryUsage,
                errorCount: metrics.errors.length,
                apiCallCount: Array.from(metrics.apiCalls.values())
                    .reduce((sum, api) => sum + api.count, 0)
            },
            details: {
                slowestApis: Array.from(metrics.apiCalls.entries())
                    .sort(([,a], [,b]) => b.averageTime - a.averageTime)
                    .slice(0, 5),
                recentErrors: metrics.errors.slice(-10),
                websocketMessages: metrics.websocketMessages
            }
        };
    }
    
    exportMetrics() {
        const report = this.getPerformanceReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-report-${new Date().toISOString()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    destroy() {
        // Nettoyer les observers
        this.observers.forEach(observer => {
            if (observer.disconnect) {
                observer.disconnect();
            }
        });
        this.observers.clear();
    }
}

// Instance globale
const performanceManager = new PerformanceManager();

// Intégration avec le gestionnaire de données
if (window.dataManager) {
    const originalNotify = dataManager.notify.bind(dataManager);
    dataManager.notify = function(dataType, data) {
        performanceManager.recordWebSocketMessage();
        return originalNotify(dataType, data);
    };
}

// Export pour utilisation dans d'autres modules
window.performanceManager = performanceManager;
