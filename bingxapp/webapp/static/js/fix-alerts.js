/**
 * Script pour corriger les alertes de sécurité excessives
 * À exécuter dans la console du navigateur
 */

// Réduire la sensibilité du monitoring de sécurité
if (window.securityMonitor) {
    console.log('🔧 Correction du monitoring de sécurité...');
    
    // Ajuster les règles de sécurité
    securityMonitor.securityRules.maxActionsPerMinute = 100;
    securityMonitor.securityRules.sessionTimeout = 2 * 60 * 60 * 1000; // 2 heures
    
    // Vider les activités suspectes existantes
    securityMonitor.suspiciousActivities = [];
    
    // Désactiver temporairement le monitoring DOM
    const originalCheckForSuspiciousElements = securityMonitor.checkForSuspiciousElements;
    securityMonitor.checkForSuspiciousElements = function(element) {
        // Ne rien faire - désactivé
    };
    
    console.log('✅ Monitoring de sécurité ajusté');
}

// Réduire les logs de performance
if (window.performanceManager) {
    console.log('🔧 Correction du monitoring de performance...');
    
    // Vider les erreurs existantes
    performanceManager.metrics.errors = [];
    
    // Ajuster le logging
    const originalLogMetric = performanceManager.logMetric;
    performanceManager.logMetric = function(name, value) {
        // Log seulement les métriques importantes
        if (name.includes('Error') || name.includes('Slow')) {
            originalLogMetric.call(this, name, value);
        }
    };
    
    console.log('✅ Monitoring de performance ajusté');
}

// Afficher un message de confirmation
if (window.notificationManager) {
    notificationManager.show({
        type: 'success',
        title: 'Système optimisé',
        message: 'Les alertes de sécurité ont été ajustées pour réduire les faux positifs',
        duration: 3000
    });
}

console.log('🎉 Corrections appliquées avec succès !');
console.log('📝 Les alertes de sécurité sont maintenant moins sensibles');
console.log('🔄 Rechargez la page pour appliquer toutes les corrections');
