/**
 * Gestionnaire de graphiques avancés avec TradingView Lightweight Charts
 */

class AdvancedChartManager {
    constructor() {
        this.charts = new Map();
        this.priceData = new Map();
        this.volumeData = new Map();
        this.indicators = new Map();
        this.themes = {
            dark: {
                layout: {
                    background: { color: '#1a1a1a' },
                    textColor: '#ecf0f1',
                },
                grid: {
                    vertLines: { color: '#2d2d2d' },
                    horzLines: { color: '#2d2d2d' },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                priceScale: {
                    borderColor: '#444',
                },
                timeScale: {
                    borderColor: '#444',
                },
            },
            light: {
                layout: {
                    background: { color: '#ffffff' },
                    textColor: '#212529',
                },
                grid: {
                    vertLines: { color: '#f0f0f0' },
                    horzLines: { color: '#f0f0f0' },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                priceScale: {
                    borderColor: '#cccccc',
                },
                timeScale: {
                    borderColor: '#cccccc',
                },
            }
        };
        
        this.init();
    }
    
    init() {
        // Écouter les changements de thème
        document.addEventListener('themeChanged', (event) => {
            this.updateChartsTheme(event.detail.theme);
        });
        
        // Écouter les données de marché
        stateManager.subscribe('market.symbols', (symbols) => {
            this.updateChartsData(symbols);
        });
    }
    
    createChart(containerId, symbol, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('Conteneur de graphique non trouvé:', containerId);
            return null;
        }
        
        // Configuration par défaut
        const defaultOptions = {
            width: container.clientWidth,
            height: options.height || 400,
            ...this.getCurrentTheme(),
            rightPriceScale: {
                scaleMargins: {
                    top: 0.1,
                    bottom: 0.1,
                },
            },
            timeScale: {
                timeVisible: true,
                secondsVisible: false,
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            handleScroll: {
                mouseWheel: true,
                pressedMouseMove: true,
            },
            handleScale: {
                axisPressedMouseMove: true,
                mouseWheel: true,
                pinch: true,
            },
        };
        
        // Créer le graphique
        const chart = LightweightCharts.createChart(container, {
            ...defaultOptions,
            ...options
        });
        
        // Créer les séries
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });
        
        const volumeSeries = chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // Configurer l'échelle de volume
        chart.priceScale('volume').applyOptions({
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // Stocker les références
        const chartData = {
            chart,
            candlestickSeries,
            volumeSeries,
            symbol,
            container,
            indicators: new Map()
        };
        
        this.charts.set(containerId, chartData);
        
        // Gérer le redimensionnement
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                const { width, height } = entry.contentRect;
                chart.applyOptions({ width, height: height || 400 });
            }
        });
        resizeObserver.observe(container);
        
        // Charger les données initiales
        this.loadInitialData(symbol, chartData);
        
        return chartData;
    }
    
    async loadInitialData(symbol, chartData) {
        try {
            // Générer des données de démonstration
            const data = this.generateDemoData();
            
            chartData.candlestickSeries.setData(data.candles);
            chartData.volumeSeries.setData(data.volume);
            
            // Stocker les données
            this.priceData.set(symbol, data.candles);
            this.volumeData.set(symbol, data.volume);
            
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
        }
    }
    
    generateDemoData() {
        const data = [];
        const volumeData = [];
        const baseTime = Math.floor(Date.now() / 1000) - 86400; // 24h ago
        let price = 45000 + Math.random() * 10000;
        
        for (let i = 0; i < 100; i++) {
            const time = baseTime + i * 900; // 15 minutes intervals
            const change = (Math.random() - 0.5) * 1000;
            const open = price;
            const close = price + change;
            const high = Math.max(open, close) + Math.random() * 500;
            const low = Math.min(open, close) - Math.random() * 500;
            const volume = Math.random() * 1000000;
            
            data.push({
                time,
                open,
                high,
                low,
                close
            });
            
            volumeData.push({
                time,
                value: volume,
                color: close > open ? '#26a69a' : '#ef5350'
            });
            
            price = close;
        }
        
        return { candles: data, volume: volumeData };
    }
    
    updatePrice(symbol, price) {
        // Mettre à jour le prix en temps réel
        for (const [containerId, chartData] of this.charts.entries()) {
            if (chartData.symbol === symbol) {
                const time = Math.floor(Date.now() / 1000);
                
                // Mettre à jour la dernière bougie
                const lastCandle = this.priceData.get(symbol)?.slice(-1)[0];
                if (lastCandle) {
                    const updatedCandle = {
                        ...lastCandle,
                        close: price,
                        high: Math.max(lastCandle.high, price),
                        low: Math.min(lastCandle.low, price),
                        time
                    };
                    
                    chartData.candlestickSeries.update(updatedCandle);
                }
            }
        }
    }
    
    addIndicator(containerId, type, options = {}) {
        const chartData = this.charts.get(containerId);
        if (!chartData) return null;
        
        let indicator;
        
        switch (type) {
            case 'sma':
                indicator = this.addSMA(chartData, options);
                break;
            case 'ema':
                indicator = this.addEMA(chartData, options);
                break;
            case 'rsi':
                indicator = this.addRSI(chartData, options);
                break;
            case 'macd':
                indicator = this.addMACD(chartData, options);
                break;
            default:
                console.warn('Type d\'indicateur non supporté:', type);
                return null;
        }
        
        if (indicator) {
            chartData.indicators.set(type, indicator);
        }
        
        return indicator;
    }
    
    addSMA(chartData, options = {}) {
        const period = options.period || 20;
        const color = options.color || '#f39c12';
        
        const smaData = this.calculateSMA(this.priceData.get(chartData.symbol), period);
        
        const smaSeries = chartData.chart.addLineSeries({
            color,
            lineWidth: 2,
            title: `SMA(${period})`
        });
        
        smaSeries.setData(smaData);
        
        return smaSeries;
    }
    
    addEMA(chartData, options = {}) {
        const period = options.period || 20;
        const color = options.color || '#e74c3c';
        
        const emaData = this.calculateEMA(this.priceData.get(chartData.symbol), period);
        
        const emaSeries = chartData.chart.addLineSeries({
            color,
            lineWidth: 2,
            title: `EMA(${period})`
        });
        
        emaSeries.setData(emaData);
        
        return emaSeries;
    }
    
    addRSI(chartData, options = {}) {
        const period = options.period || 14;
        
        // Créer un nouveau graphique pour le RSI
        const rsiContainer = document.createElement('div');
        rsiContainer.style.height = '150px';
        rsiContainer.style.marginTop = '10px';
        chartData.container.parentNode.appendChild(rsiContainer);
        
        const rsiChart = LightweightCharts.createChart(rsiContainer, {
            width: chartData.container.clientWidth,
            height: 150,
            ...this.getCurrentTheme(),
            rightPriceScale: {
                scaleMargins: {
                    top: 0.1,
                    bottom: 0.1,
                },
            },
        });
        
        const rsiSeries = rsiChart.addLineSeries({
            color: '#9c27b0',
            lineWidth: 2,
        });
        
        // Ajouter les lignes de référence
        const upperLine = rsiChart.addLineSeries({
            color: '#f44336',
            lineWidth: 1,
            lineStyle: LightweightCharts.LineStyle.Dashed,
        });
        
        const lowerLine = rsiChart.addLineSeries({
            color: '#4caf50',
            lineWidth: 1,
            lineStyle: LightweightCharts.LineStyle.Dashed,
        });
        
        const rsiData = this.calculateRSI(this.priceData.get(chartData.symbol), period);
        const upperData = rsiData.map(point => ({ time: point.time, value: 70 }));
        const lowerData = rsiData.map(point => ({ time: point.time, value: 30 }));
        
        rsiSeries.setData(rsiData);
        upperLine.setData(upperData);
        lowerLine.setData(lowerData);
        
        return { chart: rsiChart, series: rsiSeries };
    }
    
    calculateSMA(data, period) {
        const smaData = [];
        
        for (let i = period - 1; i < data.length; i++) {
            let sum = 0;
            for (let j = 0; j < period; j++) {
                sum += data[i - j].close;
            }
            
            smaData.push({
                time: data[i].time,
                value: sum / period
            });
        }
        
        return smaData;
    }
    
    calculateEMA(data, period) {
        const emaData = [];
        const multiplier = 2 / (period + 1);
        
        // Premier EMA = SMA
        let ema = data.slice(0, period).reduce((sum, candle) => sum + candle.close, 0) / period;
        emaData.push({ time: data[period - 1].time, value: ema });
        
        for (let i = period; i < data.length; i++) {
            ema = (data[i].close - ema) * multiplier + ema;
            emaData.push({ time: data[i].time, value: ema });
        }
        
        return emaData;
    }
    
    calculateRSI(data, period) {
        const rsiData = [];
        
        for (let i = period; i < data.length; i++) {
            let gains = 0;
            let losses = 0;
            
            for (let j = 1; j <= period; j++) {
                const change = data[i - j + 1].close - data[i - j].close;
                if (change > 0) {
                    gains += change;
                } else {
                    losses -= change;
                }
            }
            
            const avgGain = gains / period;
            const avgLoss = losses / period;
            const rs = avgGain / avgLoss;
            const rsi = 100 - (100 / (1 + rs));
            
            rsiData.push({
                time: data[i].time,
                value: rsi
            });
        }
        
        return rsiData;
    }
    
    getCurrentTheme() {
        const currentTheme = stateManager.getState('ui.theme') || 'dark';
        return this.themes[currentTheme] || this.themes.dark;
    }
    
    updateChartsTheme(themeName) {
        const theme = this.themes[themeName] || this.themes.dark;
        
        for (const [containerId, chartData] of this.charts.entries()) {
            chartData.chart.applyOptions(theme);
        }
    }
    
    removeChart(containerId) {
        const chartData = this.charts.get(containerId);
        if (chartData) {
            chartData.chart.remove();
            this.charts.delete(containerId);
        }
    }
    
    resizeChart(containerId, width, height) {
        const chartData = this.charts.get(containerId);
        if (chartData) {
            chartData.chart.applyOptions({ width, height });
        }
    }
    
    exportChart(containerId) {
        const chartData = this.charts.get(containerId);
        if (chartData) {
            // Créer un canvas pour l'export
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Cette fonctionnalité nécessiterait une implémentation plus complexe
            // pour capturer le contenu du graphique TradingView
            console.log('Export de graphique non encore implémenté');
        }
    }
}

// Instance globale
const advancedChartManager = new AdvancedChartManager();

// Fonctions utilitaires globales
function createAdvancedChart(containerId, symbol, options = {}) {
    return advancedChartManager.createChart(containerId, symbol, options);
}

function addIndicator(containerId, type, options = {}) {
    return advancedChartManager.addIndicator(containerId, type, options);
}

// Export pour utilisation dans d'autres modules
window.advancedChartManager = advancedChartManager;
window.createAdvancedChart = createAdvancedChart;
window.addIndicator = addIndicator;
