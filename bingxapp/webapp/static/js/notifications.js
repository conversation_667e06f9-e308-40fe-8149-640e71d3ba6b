/**
 * Système de notifications amélioré pour l'application BingX Trading
 */

class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        // Créer le conteneur de notifications s'il n'existe pas
        this.container = document.getElementById('notification-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }
    
    setupEventListeners() {
        // Écouter les changements d'état des notifications
        stateManager.subscribe('ui.notifications', (notifications) => {
            this.updateDisplay(notifications);
        });
        
        // Écouter les événements de trading pour les notifications automatiques
        stateManager.subscribe('account.positions', () => {
            this.show({
                type: 'info',
                title: 'Positions mises à jour',
                message: 'Vos positions ont été actualisées',
                duration: 3000
            });
        });
    }
    
    show(options) {
        const notification = {
            id: options.id || Date.now(),
            type: options.type || 'info', // success, error, warning, info
            title: options.title || '',
            message: options.message || '',
            duration: options.duration || this.defaultDuration,
            persistent: options.persistent || false,
            actions: options.actions || [],
            timestamp: new Date()
        };
        
        // Ajouter au state manager
        stateManager.addNotification(notification);
        
        return notification.id;
    }
    
    updateDisplay(notifications) {
        // Limiter le nombre de notifications affichées
        const visibleNotifications = notifications.slice(0, this.maxNotifications);
        
        // Vider le conteneur
        this.container.innerHTML = '';
        
        // Afficher chaque notification
        visibleNotifications.forEach(notification => {
            const element = this.createNotificationElement(notification);
            this.container.appendChild(element);
            
            // Animation d'entrée
            setTimeout(() => {
                element.classList.add('show');
            }, 10);
        });
    }
    
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type}`;
        element.dataset.id = notification.id;
        
        const icon = this.getIcon(notification.type);
        const timeAgo = this.getTimeAgo(notification.timestamp);
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <i class="${icon}"></i>
                    <span class="notification-title">${notification.title}</span>
                    <span class="notification-time">${timeAgo}</span>
                    <button class="notification-close" onclick="notificationManager.hide('${notification.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-message">${notification.message}</div>
                ${this.createActionsHTML(notification.actions)}
            </div>
            ${!notification.persistent ? `<div class="notification-progress"></div>` : ''}
        `;
        
        // Ajouter la barre de progression si non persistante
        if (!notification.persistent && notification.duration > 0) {
            const progressBar = element.querySelector('.notification-progress');
            if (progressBar) {
                progressBar.style.animationDuration = `${notification.duration}ms`;
            }
        }
        
        return element;
    }
    
    createActionsHTML(actions) {
        if (!actions || actions.length === 0) return '';
        
        const actionsHTML = actions.map(action => `
            <button class="notification-action btn btn-sm btn-outline-light" 
                    onclick="${action.callback}">
                ${action.label}
            </button>
        `).join('');
        
        return `<div class="notification-actions">${actionsHTML}</div>`;
    }
    
    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    getTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const seconds = Math.floor(diff / 1000);
        
        if (seconds < 60) return 'À l\'instant';
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
        if (seconds < 86400) return `${Math.floor(seconds / 3600)}h`;
        return `${Math.floor(seconds / 86400)}j`;
    }
    
    hide(id) {
        stateManager.removeNotification(id);
    }
    
    clear() {
        stateManager.setState('ui.notifications', []);
    }
    
    // Notifications prédéfinies pour le trading
    showOrderSuccess(order) {
        return this.show({
            type: 'success',
            title: 'Ordre exécuté',
            message: `${order.side} ${order.quantity} ${order.symbol} à ${order.price}`,
            actions: [
                {
                    label: 'Voir détails',
                    callback: `showOrderDetails('${order.id}')`
                }
            ]
        });
    }
    
    showOrderError(error) {
        return this.show({
            type: 'error',
            title: 'Erreur d\'ordre',
            message: error.message || 'Une erreur est survenue',
            persistent: true
        });
    }
    
    showConnectionStatus(connected) {
        if (connected) {
            return this.show({
                type: 'success',
                title: 'Connexion établie',
                message: 'Données en temps réel activées',
                duration: 3000
            });
        } else {
            return this.show({
                type: 'warning',
                title: 'Connexion perdue',
                message: 'Tentative de reconnexion...',
                persistent: true
            });
        }
    }
    
    showPriceAlert(symbol, price, condition) {
        return this.show({
            type: 'warning',
            title: 'Alerte de prix',
            message: `${symbol} ${condition} ${price}`,
            actions: [
                {
                    label: 'Trader',
                    callback: `openTradingModal('${symbol}')`
                }
            ]
        });
    }
    
    showSystemMessage(message, type = 'info') {
        return this.show({
            type: type,
            title: 'Message système',
            message: message,
            duration: 8000
        });
    }
}

// Instance globale
const notificationManager = new NotificationManager();

// Fonctions utilitaires globales
function showOrderDetails(orderId) {
    // Implémenter l'affichage des détails d'ordre
    console.log('Affichage détails ordre:', orderId);
}

function openTradingModal(symbol) {
    // Implémenter l'ouverture du modal de trading
    console.log('Ouverture modal trading pour:', symbol);
}

// Export pour utilisation dans d'autres modules
window.notificationManager = notificationManager;
