{% extends "base.html" %}

{% block title %}BingX Trading - Connexion{% endblock %}

{% block extra_css %}
<style>
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
}

.dashboard-options {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

.dashboard-option {
    display: block;
    padding: 15px;
    margin: 10px 0;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.dashboard-option:hover {
    background: #e9ecef;
    border-color: #3498db;
    text-decoration: none;
    color: #2c3e50;
    transform: translateY(-2px);
}

.option-title {
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.option-desc {
    font-size: 0.9em;
    color: #7f8c8d;
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="text-center mb-4">
            <h1><i class="fas fa-chart-line"></i> BingX Trading</h1>
            <p class="text-muted">Interface de Trading Professionnel</p>
        </div>

        <!-- Formulaire de connexion -->
        <form id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">Nom d'utilisateur</label>
                <input type="text" class="form-control" id="username" name="username" value="admin" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Mot de passe</label>
                <input type="password" class="form-control" id="password" name="password" value="admin123" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt"></i> Se connecter
            </button>
        </form>

        <!-- Alertes -->
        <div id="loginAlert" class="alert alert-danger mt-3" style="display: none;"></div>

        <!-- Options de dashboard -->
        <div class="dashboard-options">
            <h6 class="text-center text-muted">Versions du Dashboard Disponibles</h6>
            
            <a href="#" onclick="loginAndRedirect('/dashboard')" class="dashboard-option">
                <div class="option-title">🚀 Dashboard Principal (Recommandé)</div>
                <div class="option-desc">Interface complète avec données BingX en temps réel</div>
            </a>

            <a href="#" onclick="loginAndRedirect('/dashboard')" class="dashboard-option">
                <div class="option-title">📊 Dashboard Complet</div>
                <div class="option-desc">Interface complète avec toutes les fonctionnalités de trading</div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let redirectUrl = '/dashboard'; // Par défaut

document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const alertDiv = document.getElementById('loginAlert');
    
    try {
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);
        
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const data = await response.json();
            localStorage.setItem('authToken', data.access_token);
            
            // Redirection vers le dashboard choisi
            window.location.href = redirectUrl;
        } else {
            const errorData = await response.json();
            alertDiv.textContent = errorData.detail || 'Erreur de connexion';
            alertDiv.style.display = 'block';
        }
    } catch (error) {
        alertDiv.textContent = 'Erreur de connexion: ' + error.message;
        alertDiv.style.display = 'block';
    }
});

function loginAndRedirect(url) {
    redirectUrl = url;
    document.getElementById('loginForm').dispatchEvent(new Event('submit'));
}

// Vérifier si déjà connecté
if (localStorage.getItem('authToken')) {
    window.location.href = redirectUrl;
}
</script>
{% endblock %}
