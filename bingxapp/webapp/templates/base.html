<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BingX Trading{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- TradingView Charting Library -->
    <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js"></script>
    <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-technical-analysis.js"></script>
    <!-- TradingView Lightweight Charts -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>

    <!-- Styles personnalisés -->
    <link href="{{ url_for('static', path='css/style.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #f39c12;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --text-light: #ecf0f1;
            --text-muted: #95a5a6;
        }

        body {
            background-color: var(--primary-color);
            color: var(--text-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background-color: var(--secondary-color) !important;
            border-bottom: 2px solid var(--accent-color);
        }

        .navbar-brand {
            color: var(--accent-color) !important;
            font-weight: bold;
        }

        .card {
            background-color: var(--secondary-color);
            border: 1px solid #444;
            border-radius: 10px;
        }

        .card-header {
            background-color: rgba(243, 156, 18, 0.1);
            border-bottom: 1px solid var(--accent-color);
            color: var(--accent-color);
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-primary:hover {
            background-color: #e67e22;
            border-color: #e67e22;
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .table-dark {
            background-color: var(--secondary-color);
        }

        .table-dark td, .table-dark th {
            border-color: #444;
        }

        .form-control {
            background-color: var(--secondary-color);
            border-color: #444;
            color: var(--text-light);
        }

        .form-control:focus {
            background-color: var(--secondary-color);
            border-color: var(--accent-color);
            color: var(--text-light);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        .alert-info {
            background-color: rgba(52, 152, 219, 0.1);
            border-color: #3498db;
            color: #3498db;
        }

        .alert-success {
            background-color: rgba(39, 174, 96, 0.1);
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.1);
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        .price-up {
            color: var(--success-color);
        }

        .price-down {
            color: var(--danger-color);
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--danger-color);
        }

        .sidebar {
            background-color: var(--secondary-color);
            min-height: calc(100vh - 56px);
            border-right: 1px solid #444;
        }

        .sidebar .nav-link {
            color: var(--text-light);
            padding: 15px 20px;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--accent-color);
        }

        .sidebar .nav-link.active {
            background-color: var(--accent-color);
            color: var(--primary-color);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: var(--text-muted);
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .order-form {
            background-color: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .websocket-status {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1000;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> BingX Trading
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTrading()">
                            <i class="fas fa-exchange-alt"></i> Trading
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPositions()">
                            <i class="fas fa-list"></i> Positions
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <span class="status-indicator" id="connectionStatus"></span>
                            <span id="connectionText">Connexion...</span>
                        </span>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm me-2 alerts-button" onclick="openAlertsModal()">
                            <i class="fas fa-bell"></i> Alertes
                            <span class="alerts-badge" style="display: none;">0</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="advancedOrderManager.openOrdersModal()">
                            <i class="fas fa-cogs"></i> Ordres
                            <span class="orders-badge" style="display: none;">0</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Déconnexion
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Indicateur de statut WebSocket -->
    <div id="websocketStatus" class="websocket-status"></div>

    <!-- Contenu principal -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Scripts communs -->
    <script>
        // Variables globales
        let websocket = null;
        let authToken = localStorage.getItem('authToken');
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            initWebSocket();
        });

        // Vérification de l'authentification
        function checkAuth() {
            if (!authToken && window.location.pathname !== '/') {
                window.location.href = '/';
                return false;
            }
            return true;
        }

        // Initialisation WebSocket
        function initWebSocket() {
            if (!authToken) return;
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                updateConnectionStatus(true);
                showWebSocketStatus('Connecté', 'success');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            websocket.onclose = function(event) {
                updateConnectionStatus(false);
                showWebSocketStatus('Déconnecté', 'danger');
                // Tentative de reconnexion après 5 secondes
                setTimeout(initWebSocket, 5000);
            };
            
            websocket.onerror = function(error) {
                console.error('Erreur WebSocket:', error);
                showWebSocketStatus('Erreur de connexion', 'danger');
            };
        }

        // Mise à jour du statut de connexion
        function updateConnectionStatus(connected) {
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = 'En ligne';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Hors ligne';
            }
        }

        // Affichage du statut WebSocket
        function showWebSocketStatus(message, type) {
            const statusDiv = document.getElementById('websocketStatus');
            statusDiv.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // Auto-hide après 3 secondes
            setTimeout(() => {
                const alert = statusDiv.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }

        // Gestionnaire des messages WebSocket
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'market_data':
                    updateMarketData(data.symbol, data.data);
                    break;
                case 'positions':
                    updatePositions(data.data);
                    break;
                case 'order_placed':
                    showNotification('Ordre placé avec succès', 'success');
                    break;
                default:
                    console.log('Message WebSocket non géré:', data);
            }
        }

        // Fonctions utilitaires
        function formatNumber(num, decimals = 2) {
            return parseFloat(num).toLocaleString('fr-FR', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        function formatCurrency(amount, currency = 'USDT') {
            return `${formatNumber(amount)} ${currency}`;
        }

        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto-remove après 5 secondes
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Déconnexion
        function logout() {
            localStorage.removeItem('authToken');
            if (websocket) {
                websocket.close();
            }
            window.location.href = '/';
        }

        // Fonctions de navigation (à implémenter dans les pages spécifiques)
        function showTrading() {
            // À implémenter
        }

        function showPositions() {
            // À implémenter
        }

        function updateMarketData(symbol, data) {
            // À implémenter dans les pages spécifiques
        }

        function updatePositions(positions) {
            // À implémenter dans les pages spécifiques
        }
    </script>

    <!-- Scripts améliorés -->
    <script src="{{ url_for('static', path='js/performance.js') }}"></script>
    <script src="{{ url_for('static', path='js/notifications.js') }}"></script>
    <script src="{{ url_for('static', path='js/security-monitor.js') }}"></script>
    <script src="{{ url_for('static', path='js/theme-manager.js') }}"></script>
    <script src="{{ url_for('static', path='js/advanced-charts.js') }}"></script>
    <script src="{{ url_for('static', path='js/price-alerts.js') }}"></script>
    <script src="{{ url_for('static', path='js/advanced-orders.js') }}"></script>
    <script src="{{ url_for('static', path='js/trading-modal.js') }}"></script>
    <script src="{{ url_for('static', path='js/trading.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
