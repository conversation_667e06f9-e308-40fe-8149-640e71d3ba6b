{% extends "base.html" %}

{% block title %}Dashboard - BingX Trading{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar">
            <nav class="nav flex-column">
                <a class="nav-link active" href="#" onclick="showOverview()">
                    <i class="fas fa-tachometer-alt"></i> Vue d'ensemble
                </a>
                <a class="nav-link" href="#" onclick="showTrading()">
                    <i class="fas fa-exchange-alt"></i> Trading
                </a>
                <a class="nav-link" href="#" onclick="showPositions()">
                    <i class="fas fa-list"></i> Positions
                </a>
                <a class="nav-link" href="#" onclick="showMarketData()">
                    <i class="fas fa-chart-bar"></i> Marché
                </a>
                <a class="nav-link" href="#" onclick="showHistory()">
                    <i class="fas fa-history"></i> Historique
                </a>
            </nav>
        </div>
        
        <!-- Contenu principal -->
        <div class="col-md-10">
            <!-- Vue d'ensemble -->
            <div id="overviewSection">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt"></i> Dashboard de Trading</h2>
                    <div class="text-muted">
                        <i class="fas fa-clock"></i> 
                        <span id="lastUpdate">Chargement...</span>
                    </div>
                </div>
                
                <!-- Métriques principales -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <div class="metric-value text-primary" id="totalBalance">--</div>
                                <div class="metric-label">Solde Total</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <div class="metric-value" id="totalPnL">--</div>
                                <div class="metric-label">PnL Total</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <div class="metric-value text-info" id="openPositions">--</div>
                                <div class="metric-label">Positions Ouvertes</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <div class="metric-value text-warning" id="marginUsed">--</div>
                                <div class="metric-label">Marge Utilisée</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Graphiques -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-chart-line"></i> Graphique BTC-USDT (TradingView)</h5>
                                <div class="chart-controls">
                                    <select id="chartSymbol" class="form-select form-select-sm me-2" onchange="changeChartSymbol()">
                                        <option value="BINANCE:BTCUSDT">BTC-USDT</option>
                                        <option value="BINANCE:ETHUSDT">ETH-USDT</option>
                                        <option value="BINANCE:BNBUSDT">BNB-USDT</option>
                                        <option value="BINANCE:ADAUSDT">ADA-USDT</option>
                                        <option value="BINANCE:SOLUSDT">SOL-USDT</option>
                                    </select>
                                    <select id="chartInterval" class="form-select form-select-sm" onchange="changeChartInterval()">
                                        <option value="1">1m</option>
                                        <option value="5">5m</option>
                                        <option value="15" selected>15m</option>
                                        <option value="60">1h</option>
                                        <option value="240">4h</option>
                                        <option value="D">1D</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- TradingView Widget -->
                                <div id="tradingview_chart" style="height: 500px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-book"></i> Carnet d'Ordres</h5>
                            </div>
                            <div class="card-body">
                                <div id="orderBook" style="height: 400px; overflow-y: auto;">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Chargement...
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mini graphique technique -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-area"></i> Analyse Technique</h6>
                            </div>
                            <div class="card-body p-0">
                                <!-- TradingView Technical Analysis Widget -->
                                <div id="tradingview_technical_analysis" style="height: 200px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Positions récentes -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> Positions Ouvertes</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped">
                                        <thead>
                                            <tr>
                                                <th>Symbole</th>
                                                <th>Quantité</th>
                                                <th>Prix d'entrée</th>
                                                <th>Prix actuel</th>
                                                <th>PnL</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="positionsTable">
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    <div class="loading">
                                                        <i class="fas fa-spinner fa-spin"></i> Chargement des positions...
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Section Trading -->
            <div id="tradingSection" style="display: none;">
                <h2><i class="fas fa-exchange-alt"></i> Interface de Trading</h2>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-plus"></i> Placer un Ordre</h5>
                            </div>
                            <div class="card-body">
                                <form id="orderForm" class="order-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="orderSymbol" class="form-label">Symbole</label>
                                                <select class="form-control" id="orderSymbol" required>
                                                    <option value="BTC-USDT">BTC-USDT</option>
                                                    <option value="ETH-USDT">ETH-USDT</option>
                                                    <option value="BNB-USDT">BNB-USDT</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="orderSide" class="form-label">Côté</label>
                                                <select class="form-control" id="orderSide" required>
                                                    <option value="BUY">Achat</option>
                                                    <option value="SELL">Vente</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="orderType" class="form-label">Type</label>
                                                <select class="form-control" id="orderType" required onchange="togglePriceField()">
                                                    <option value="MARKET">Marché</option>
                                                    <option value="LIMIT">Limite</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="orderQuantity" class="form-label">Quantité</label>
                                                <input type="number" class="form-control" id="orderQuantity" step="0.0001" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3" id="priceField" style="display: none;">
                                        <label for="orderPrice" class="form-label">Prix</label>
                                        <input type="number" class="form-control" id="orderPrice" step="0.01">
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-paper-plane"></i> Placer l'Ordre
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> Informations de Trading</h5>
                            </div>
                            <div class="card-body">
                                <div id="tradingInfo">
                                    <p><strong>Prix actuel BTC-USDT:</strong> <span id="currentPrice">--</span></p>
                                    <p><strong>Marge disponible:</strong> <span id="availableMargin">--</span></p>
                                    <p><strong>Spread:</strong> <span id="spread">--</span></p>
                                </div>
                                
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Attention:</strong> Les ordres placés sont réels et affecteront votre compte BingX.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Autres sections (à implémenter) -->
            <div id="positionsSection" style="display: none;">
                <h2><i class="fas fa-list"></i> Gestion des Positions</h2>
                <p>Section en cours de développement...</p>
            </div>
            
            <div id="marketDataSection" style="display: none;">
                <h2><i class="fas fa-chart-bar"></i> Données de Marché</h2>
                <p>Section en cours de développement...</p>
            </div>
            
            <div id="historySection" style="display: none;">
                <h2><i class="fas fa-history"></i> Historique</h2>
                <p>Section en cours de développement...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales pour les données
let accountData = null;
let positionsData = [];
let marketData = null;
let priceChart = null;

// Initialisation du dashboard
document.addEventListener('DOMContentLoaded', function() {
    if (!checkAuth()) return;
    
    initDashboard();
    loadInitialData();
    
    // Gestionnaire du formulaire d'ordre
    document.getElementById('orderForm').addEventListener('submit', handleOrderSubmit);
});

async function initDashboard() {
    // Initialiser le graphique des prix
    initPriceChart();
    
    // Charger les données initiales
    await loadAccountData();
    await loadPositions();
}

async function loadInitialData() {
    try {
        await Promise.all([
            loadAccountData(),
            loadPositions(),
            loadMarketData('BTC-USDT')
        ]);
        
        updateLastUpdateTime();
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showNotification('Erreur lors du chargement des données', 'danger');
    }
}

async function loadAccountData() {
    try {
        const response = await fetch('/api/account/balance', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            accountData = await response.json();
            updateAccountMetrics();
        }
    } catch (error) {
        console.error('Erreur lors du chargement du compte:', error);
    }
}

async function loadPositions() {
    try {
        const response = await fetch('/api/account/positions', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            positionsData = data.positions || [];
            updatePositionsTable();
            updatePositionsMetric();
        }
    } catch (error) {
        console.error('Erreur lors du chargement des positions:', error);
    }
}

async function loadMarketData(symbol) {
    try {
        const response = await fetch(`/api/market/${symbol}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            marketData = await response.json();
            updateMarketInfo();
            updateOrderBook();

            // Initialiser le graphique avec le premier prix
            if (marketData && marketData.ticker && marketData.ticker.data && marketData.ticker.data.lastPrice) {
                updatePriceChart(marketData.ticker.data.lastPrice);
                console.log('📊 Graphique initialisé avec le prix:', marketData.ticker.data.lastPrice);
            }
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données de marché:', error);
    }
}

function updateAccountMetrics() {
    if (!accountData || !accountData.data) return;
    
    const balance = accountData.data.balance;
    
    document.getElementById('totalBalance').textContent = formatCurrency(balance.balance);
    document.getElementById('marginUsed').textContent = `${((parseFloat(balance.usedMargin) / parseFloat(balance.balance)) * 100).toFixed(1)}%`;
    
    const pnlElement = document.getElementById('totalPnL');
    const unrealizedPnL = parseFloat(balance.unrealizedProfit);
    pnlElement.textContent = formatCurrency(unrealizedPnL);
    pnlElement.className = `metric-value ${unrealizedPnL >= 0 ? 'price-up' : 'price-down'}`;
    
    // Mettre à jour les infos de trading
    document.getElementById('availableMargin').textContent = formatCurrency(balance.availableMargin);
}

function updatePositionsMetric() {
    document.getElementById('openPositions').textContent = positionsData.length;
}

function updatePositionsTable() {
    const tbody = document.getElementById('positionsTable');
    
    if (positionsData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Aucune position ouverte</td></tr>';
        return;
    }
    
    tbody.innerHTML = positionsData.map(position => {
        const pnl = parseFloat(position.unrealizedProfit || 0);
        const pnlClass = pnl >= 0 ? 'price-up' : 'price-down';
        
        return `
            <tr>
                <td><strong>${position.symbol}</strong></td>
                <td>${formatNumber(position.positionAmt)}</td>
                <td>${position.entryPrice || '--'}</td>
                <td>--</td>
                <td class="${pnlClass}">${formatCurrency(pnl)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="closePosition('${position.symbol}')">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function updateMarketInfo() {
    if (!marketData || !marketData.ticker) return;
    
    const ticker = marketData.ticker.data;
    document.getElementById('currentPrice').textContent = formatCurrency(ticker.lastPrice);
    
    // Calculer le spread
    if (marketData.orderbook && marketData.orderbook.data) {
        const orderbook = marketData.orderbook.data;
        if (orderbook.bids.length > 0 && orderbook.asks.length > 0) {
            const bestBid = parseFloat(orderbook.bids[0][0]);
            const bestAsk = parseFloat(orderbook.asks[0][0]);
            const spread = ((bestAsk - bestBid) / bestBid * 100).toFixed(4);
            document.getElementById('spread').textContent = `${spread}%`;
        }
    }
}

function updateOrderBook() {
    if (!marketData || !marketData.orderbook) return;
    
    const orderbook = marketData.orderbook.data;
    const orderBookDiv = document.getElementById('orderBook');
    
    let html = '<div class="row"><div class="col-6"><h6 class="text-success">Achats</h6>';
    
    // Bids (achats)
    orderbook.bids.slice(0, 10).forEach(([price, quantity]) => {
        html += `
            <div class="d-flex justify-content-between small">
                <span class="text-success">${formatNumber(price)}</span>
                <span>${formatNumber(quantity, 4)}</span>
            </div>
        `;
    });
    
    html += '</div><div class="col-6"><h6 class="text-danger">Ventes</h6>';
    
    // Asks (ventes)
    orderbook.asks.slice(0, 10).forEach(([price, quantity]) => {
        html += `
            <div class="d-flex justify-content-between small">
                <span class="text-danger">${formatNumber(price)}</span>
                <span>${formatNumber(quantity, 4)}</span>
            </div>
        `;
    });
    
    html += '</div></div>';
    orderBookDiv.innerHTML = html;
}

function initPriceChart() {
    // Configuration du widget TradingView Advanced Chart
    const chartScript = document.createElement('script');
    chartScript.type = 'text/javascript';
    chartScript.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js';
    chartScript.async = true;
    chartScript.innerHTML = JSON.stringify({
        "width": "100%",
        "height": "500",
        "symbol": "BINANCE:BTCUSDT",
        "interval": "15",
        "timezone": "Etc/UTC",
        "theme": "dark",
        "style": "1",
        "locale": "fr",
        "toolbar_bg": "#1a1a1a",
        "enable_publishing": false,
        "allow_symbol_change": true,
        "container_id": "tradingview_chart",
        "studies": [
            "Volume@tv-basicstudies"
        ],
        "show_popup_button": true,
        "popup_width": "1000",
        "popup_height": "650",
        "hide_top_toolbar": false,
        "hide_legend": false,
        "save_image": false,
        "calendar": false,
        "hide_volume": false,
        "support_host": "https://www.tradingview.com"
    });
    
    document.getElementById('tradingview_chart').appendChild(chartScript);

    // Configuration du widget d'analyse technique
    const technicalScript = document.createElement('script');
    technicalScript.type = 'text/javascript';
    technicalScript.src = 'https://s3.tradingview.com/external-embedding/embed-widget-technical-analysis.js';
    technicalScript.async = true;
    technicalScript.innerHTML = JSON.stringify({
        "interval": "15m",
        "width": "100%",
        "height": "200",
        "isTransparent": false,
        "symbol": "BINANCE:BTCUSDT",
        "showIntervalTabs": true,
        "locale": "fr",
        "colorTheme": "dark"
    });
    
    document.getElementById('tradingview_technical_analysis').appendChild(technicalScript);

    console.log('✅ Graphiques TradingView initialisés');
}

function updatePriceChart(price) {
    // TradingView se met à jour automatiquement en temps réel
    // Pas besoin de mise à jour manuelle comme avec Plotly
    console.log(`📈 Prix reçu: ${price} USDT (TradingView se met à jour automatiquement)`);
}

function changeChartSymbol() {
    const symbol = document.getElementById('chartSymbol').value;
    const interval = document.getElementById('chartInterval').value;
    
    // Vider les conteneurs
    document.getElementById('tradingview_chart').innerHTML = '';
    document.getElementById('tradingview_technical_analysis').innerHTML = '';
    
    // Attendre un peu puis recréer les widgets
    setTimeout(() => {
        // Graphique principal
        const chartScript = document.createElement('script');
        chartScript.type = 'text/javascript';
        chartScript.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js';
        chartScript.async = true;
        chartScript.innerHTML = JSON.stringify({
            "width": "100%",
            "height": "500",
            "symbol": symbol,
            "interval": interval,
            "timezone": "Etc/UTC",
            "theme": "dark",
            "style": "1",
            "locale": "fr",
            "toolbar_bg": "#1a1a1a",
            "enable_publishing": false,
            "allow_symbol_change": true,
            "container_id": "tradingview_chart",
            "studies": [
                "Volume@tv-basicstudies"
            ],
            "show_popup_button": true,
            "popup_width": "1000",
            "popup_height": "650",
            "hide_top_toolbar": false,
            "hide_legend": false,
            "save_image": false,
            "calendar": false,
            "hide_volume": false,
            "support_host": "https://www.tradingview.com"
        });
        
        document.getElementById('tradingview_chart').appendChild(chartScript);

        // Analyse technique
        const technicalScript = document.createElement('script');
        technicalScript.type = 'text/javascript';
        technicalScript.src = 'https://s3.tradingview.com/external-embedding/embed-widget-technical-analysis.js';
        technicalScript.async = true;
        technicalScript.innerHTML = JSON.stringify({
            "interval": interval + "m",
            "width": "100%",
            "height": "200",
            "isTransparent": false,
            "symbol": symbol,
            "showIntervalTabs": true,
            "locale": "fr",
            "colorTheme": "dark"
        });
        
        document.getElementById('tradingview_technical_analysis').appendChild(technicalScript);

        console.log(`📊 Graphiques mis à jour pour ${symbol} en ${interval}m`);
    }, 100);
}

function changeChartInterval() {
    changeChartSymbol(); // Utilise la même logique
}

function updateLastUpdateTime() {
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('fr-FR');
}

// Gestionnaires d'événements WebSocket
function updateMarketData(symbol, data) {
    if (symbol === 'BTC-USDT') {
        marketData = data;
        updateMarketInfo();
        updateOrderBook();

        // Mettre à jour le graphique avec le nouveau prix
        if (data && data.ticker && data.ticker.data && data.ticker.data.lastPrice) {
            updatePriceChart(data.ticker.data.lastPrice);
        }
    }
}

function updatePositions(positions) {
    positionsData = positions;
    updatePositionsTable();
    updatePositionsMetric();
}

// Navigation
function showOverview() {
    hideAllSections();
    document.getElementById('overviewSection').style.display = 'block';
    updateActiveNav(0);
}

function showTrading() {
    hideAllSections();
    document.getElementById('tradingSection').style.display = 'block';
    updateActiveNav(1);
}

function showPositions() {
    hideAllSections();
    document.getElementById('positionsSection').style.display = 'block';
    updateActiveNav(2);
}

function showMarketData() {
    hideAllSections();
    document.getElementById('marketDataSection').style.display = 'block';
    updateActiveNav(3);
}

function showHistory() {
    hideAllSections();
    document.getElementById('historySection').style.display = 'block';
    updateActiveNav(4);
}

function hideAllSections() {
    ['overviewSection', 'tradingSection', 'positionsSection', 'marketDataSection', 'historySection'].forEach(id => {
        document.getElementById(id).style.display = 'none';
    });
}

function updateActiveNav(index) {
    document.querySelectorAll('.sidebar .nav-link').forEach((link, i) => {
        link.classList.toggle('active', i === index);
    });
}

// Trading
function togglePriceField() {
    const orderType = document.getElementById('orderType').value;
    const priceField = document.getElementById('priceField');
    priceField.style.display = orderType === 'LIMIT' ? 'block' : 'none';
}

async function handleOrderSubmit(e) {
    e.preventDefault();
    
    const orderData = {
        symbol: document.getElementById('orderSymbol').value,
        side: document.getElementById('orderSide').value,
        order_type: document.getElementById('orderType').value,
        quantity: parseFloat(document.getElementById('orderQuantity').value)
    };
    
    if (orderData.order_type === 'LIMIT') {
        orderData.price = parseFloat(document.getElementById('orderPrice').value);
    }
    
    // Confirmation
    const confirmMessage = `Confirmer l'ordre ${orderData.side} de ${orderData.quantity} ${orderData.symbol} ?`;
    if (!confirm(confirmMessage)) return;
    
    try {
        const response = await fetch('/api/trading/order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify(orderData)
        });
        
        if (response.ok) {
            const result = await response.json();
            showNotification('Ordre placé avec succès', 'success');
            document.getElementById('orderForm').reset();
            
            // Recharger les données
            await loadPositions();
            await loadAccountData();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Erreur lors du placement de l\'ordre');
        }
    } catch (error) {
        console.error('Erreur ordre:', error);
        showNotification(error.message, 'danger');
    }
}

async function closePosition(symbol) {
    if (!confirm(`Fermer la position ${symbol} ?`)) return;
    
    // Implémentation à ajouter
    showNotification('Fonctionnalité en cours de développement', 'info');
}
</script>
{% endblock %}
