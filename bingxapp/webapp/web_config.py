"""
Configuration spécifique pour l'application web BingX
"""

import os
from dataclasses import dataclass


@dataclass
class WebAppConfig:
    """Configuration pour l'application web"""
    
    # Configuration du serveur web
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    reload: bool = False
    
    # Configuration de sécurité
    secret_key: str = "your-secret-key-change-this-in-production"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # Configuration de l'authentification
    admin_username: str = "admin"
    admin_password: str = "admin123"  # À changer en production !
    
    # Configuration WebSocket
    websocket_ping_interval: int = 20
    websocket_ping_timeout: int = 10
    
    # Configuration des données
    market_data_update_interval: int = 5  # secondes
    position_update_interval: int = 10    # secondes
    
    @classmethod
    def from_env(cls) -> 'WebAppConfig':
        """Créer la configuration à partir des variables d'environnement"""
        return cls(
            host=os.getenv('WEBAPP_HOST', cls.host),
            port=int(os.getenv('WEBAPP_PORT', str(cls.port))),
            debug=os.getenv('WEBAPP_DEBUG', 'false').lower() == 'true',
            reload=os.getenv('WEBAPP_RELOAD', 'false').lower() == 'true',
            secret_key=os.getenv('WEBAPP_SECRET_KEY', cls.secret_key),
            access_token_expire_minutes=int(os.getenv('WEBAPP_TOKEN_EXPIRE', str(cls.access_token_expire_minutes))),
            admin_username=os.getenv('WEBAPP_ADMIN_USER', cls.admin_username),
            admin_password=os.getenv('WEBAPP_ADMIN_PASS', cls.admin_password),
            websocket_ping_interval=int(os.getenv('WEBAPP_WS_PING_INTERVAL', str(cls.websocket_ping_interval))),
            websocket_ping_timeout=int(os.getenv('WEBAPP_WS_PING_TIMEOUT', str(cls.websocket_ping_timeout))),
            market_data_update_interval=int(os.getenv('WEBAPP_MARKET_UPDATE', str(cls.market_data_update_interval))),
            position_update_interval=int(os.getenv('WEBAPP_POSITION_UPDATE', str(cls.position_update_interval)))
        )


# Configuration globale
webapp_config = WebAppConfig.from_env()
