# Makefile pour l'application web BingX Trading

.PHONY: help install run dev check clean test

# Variables
PYTHON = python
PORT = 8000
HOST = 0.0.0.0

help: ## Affiche cette aide
	@echo "🌐 Application Web BingX Trading - Commandes disponibles:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

install: ## Installe les dépendances de l'application web
	@echo "📦 Installation des dépendances web..."
	$(PYTHON) -m pip install -r requirements.txt
	@echo "✅ Dépendances installées"

check: ## Vérifie les prérequis et la configuration
	@echo "🔍 Vérification des prérequis..."
	$(PYTHON) run.py --check-only

run: ## Lance l'application web en mode production
	@echo "🚀 Lancement de l'application web..."
	$(PYTHON) run.py --host $(HOST) --port $(PORT)

dev: ## Lance l'application web en mode développement
	@echo "🛠️  Lancement en mode développement..."
	$(PYTHON) run.py --host $(HOST) --port $(PORT) --debug --reload

dev-local: ## Lance l'application web en local uniquement
	@echo "🏠 Lancement en local..."
	$(PYTHON) run.py --host 127.0.0.1 --port $(PORT) --debug --reload

test-auth: ## Teste l'authentification
	@echo "🔐 Test de l'authentification..."
	curl -X POST "http://$(HOST):$(PORT)/api/auth/login" \
		-H "Content-Type: application/x-www-form-urlencoded" \
		-d "username=admin&password=admin123"

test-api: ## Teste les endpoints de l'API
	@echo "🧪 Test des endpoints API..."
	@echo "1. Test de l'heure du serveur..."
	curl -s "http://$(HOST):$(PORT)/api/server/time" || echo "Endpoint non disponible"
	@echo "\n2. Test de l'authentification..."
	curl -X POST "http://$(HOST):$(PORT)/api/auth/login" \
		-H "Content-Type: application/x-www-form-urlencoded" \
		-d "username=admin&password=admin123" | head -100

open: ## Ouvre l'application dans le navigateur
	@echo "🌐 Ouverture dans le navigateur..."
	@if command -v xdg-open > /dev/null; then \
		xdg-open "http://$(HOST):$(PORT)"; \
	elif command -v open > /dev/null; then \
		open "http://$(HOST):$(PORT)"; \
	else \
		echo "Ouvrez manuellement: http://$(HOST):$(PORT)"; \
	fi

logs: ## Affiche les logs de l'application
	@echo "📋 Logs de l'application..."
	@if [ -f webapp.log ]; then \
		tail -f webapp.log; \
	else \
		echo "Aucun fichier de log trouvé"; \
	fi

clean: ## Nettoie les fichiers temporaires
	@echo "🧹 Nettoyage..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -f webapp.log
	@echo "✅ Nettoyage terminé"

setup: ## Configuration initiale complète
	@echo "🔧 Configuration initiale..."
	$(MAKE) install
	$(MAKE) check
	@echo "✅ Configuration terminée"

docker-build: ## Construit l'image Docker
	@echo "🐳 Construction de l'image Docker..."
	docker build -t bingx-webapp .

docker-run: ## Lance l'application avec Docker
	@echo "🐳 Lancement avec Docker..."
	docker run -p $(PORT):$(PORT) bingx-webapp

# Commandes de développement
lint: ## Vérifie le code avec flake8
	@echo "🔍 Vérification du code..."
	$(PYTHON) -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

format: ## Formate le code avec black
	@echo "🎨 Formatage du code..."
	$(PYTHON) -m black .

requirements: ## Met à jour le fichier requirements.txt
	@echo "📋 Mise à jour des requirements..."
	$(PYTHON) -m pip freeze > requirements.txt

# Commandes de monitoring
status: ## Affiche le statut de l'application
	@echo "📊 Statut de l'application..."
	@if curl -s "http://$(HOST):$(PORT)/" > /dev/null; then \
		echo "✅ Application accessible sur http://$(HOST):$(PORT)"; \
	else \
		echo "❌ Application non accessible"; \
	fi

monitor: ## Surveille l'application
	@echo "👀 Surveillance de l'application..."
	@while true; do \
		if curl -s "http://$(HOST):$(PORT)/" > /dev/null; then \
			echo "$$(date): ✅ OK"; \
		else \
			echo "$$(date): ❌ ERREUR"; \
		fi; \
		sleep 30; \
	done

# Commandes de sauvegarde
backup: ## Sauvegarde la configuration
	@echo "💾 Sauvegarde..."
	tar -czf backup_$$(date +%Y%m%d_%H%M%S).tar.gz \
		*.py templates/ static/ requirements.txt Makefile

# Aide détaillée
help-dev: ## Aide pour le développement
	@echo "🛠️  AIDE DÉVELOPPEMENT:"
	@echo ""
	@echo "🔸 make dev        - Mode développement avec rechargement auto"
	@echo "🔸 make dev-local  - Mode développement en local uniquement"
	@echo "🔸 make lint       - Vérification du code"
	@echo "🔸 make format     - Formatage du code"
	@echo "🔸 make test-api   - Test des endpoints API"

help-prod: ## Aide pour la production
	@echo "🚀 AIDE PRODUCTION:"
	@echo ""
	@echo "🔸 make run        - Lancement en production"
	@echo "🔸 make status     - Vérifier le statut"
	@echo "🔸 make monitor    - Surveillance continue"
	@echo "🔸 make logs       - Afficher les logs"
	@echo "🔸 make backup     - Sauvegarder la configuration"

help-docker: ## Aide pour Docker
	@echo "🐳 AIDE DOCKER:"
	@echo ""
	@echo "🔸 make docker-build  - Construire l'image"
	@echo "🔸 make docker-run    - Lancer avec Docker"

# Variables d'environnement pour les tests
test-env: ## Configure l'environnement de test
	@echo "🧪 Configuration de l'environnement de test..."
	export WEBAPP_DEBUG=true
	export WEBAPP_RELOAD=true
	export WEBAPP_HOST=127.0.0.1
	export WEBAPP_PORT=8001

# Commande pour démarrer rapidement
quick-start: ## Démarrage rapide (install + check + run)
	@echo "⚡ Démarrage rapide..."
	$(MAKE) install
	$(MAKE) check
	$(MAKE) dev

# Commande pour tout nettoyer et redémarrer
reset: ## Nettoie tout et redémarre
	@echo "🔄 Reset complet..."
	$(MAKE) clean
	$(MAKE) install
	$(MAKE) dev
