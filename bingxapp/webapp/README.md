# 🌐 Application Web BingX Trading

Une interface web moderne et complète pour le trading sur BingX, développée avec FastAPI et intégrée à l'application CLI existante.

## ✨ Fonctionnalités

### 🔐 Authentification et Sécurité
- ✅ Système d'authentification JWT
- ✅ Protection des routes API
- ✅ Gestion sécurisée des sessions
- ✅ Identifiants par défaut configurables

### 📊 Dashboard de Trading
- ✅ Vue d'ensemble du compte en temps réel
- ✅ Métriques principales (solde, PnL, positions)
- ✅ Graphiques interactifs des prix
- ✅ Carnet d'ordres en direct
- ✅ Tableau des positions ouvertes

### 💹 Interface de Trading
- ✅ Placement d'ordres market et limit
- ✅ Interface intuitive avec confirmations
- ✅ Gestion des positions
- ✅ Historique des transactions

### 🔄 Données en Temps Réel
- ✅ WebSocket pour les mises à jour live
- ✅ Prix et données de marché actualisés
- ✅ Notifications des ordres exécutés
- ✅ Synchronisation automatique

### 🎨 Interface Moderne
- ✅ Design responsive et mobile-friendly
- ✅ Thème sombre optimisé pour le trading
- ✅ Animations et transitions fluides
- ✅ Graphiques interactifs avec Plotly.js

## 🚀 Installation et Lancement

### Installation Rapide
```bash
cd /workspaces/TradingAgents/bingxapp/webapp

# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application (mode démo)
python simple_app.py
```

### Avec Makefile
```bash
# Installation
make install

# Lancement en développement
make dev

# Lancement en production
make run
```

### Accès à l'Application
- **URL**: http://localhost:8000
- **Identifiants par défaut**:
  - Utilisateur: `admin`
  - Mot de passe: `admin123`

## 📁 Structure du Projet

```
webapp/
├── simple_app.py          # Application principale (mode démo)
├── main.py                # Application complète avec BingX
├── auth.py                # Système d'authentification
├── config.py              # Configuration webapp
├── web_config.py          # Configuration spécifique web
├── start.py               # Script de lancement
├── run.py                 # Script de lancement avancé
├── requirements.txt       # Dépendances Python
├── Makefile              # Commandes automatisées
├── README.md             # Cette documentation
├── templates/            # Templates HTML
│   ├── base.html         # Template de base
│   ├── index.html        # Page de connexion
│   └── dashboard.html    # Dashboard principal
└── static/               # Fichiers statiques
    ├── css/
    │   └── style.css     # Styles personnalisés
    └── js/
        └── trading.js    # JavaScript pour le trading
```

## 🔧 Configuration

### Variables d'Environnement
```bash
# Configuration du serveur
WEBAPP_HOST=0.0.0.0
WEBAPP_PORT=8000
WEBAPP_DEBUG=false

# Configuration de sécurité
WEBAPP_SECRET_KEY=your-secret-key
WEBAPP_ADMIN_USER=admin
WEBAPP_ADMIN_PASS=admin123

# Configuration BingX (pour la version complète)
BINGX_API_KEY=your-api-key
BINGX_SECRET_KEY=your-secret-key
```

### Modes de Fonctionnement

#### 1. Mode Démo (Recommandé pour les tests)
```bash
python simple_app.py
```
- Données simulées
- Pas de connexion BingX requise
- Idéal pour tester l'interface

#### 2. Mode Complet (Production)
```bash
python start.py
```
- Connexion réelle à BingX
- Données en temps réel
- Trading fonctionnel

## 🎯 Utilisation

### 1. Connexion
1. Ouvrez http://localhost:8000
2. Utilisez les identifiants par défaut
3. Accédez au dashboard

### 2. Dashboard
- **Vue d'ensemble**: Métriques principales du compte
- **Graphiques**: Prix en temps réel et historique
- **Positions**: Liste des positions ouvertes
- **Carnet d'ordres**: Bids et asks en direct

### 3. Trading
1. Cliquez sur "Trading" dans la sidebar
2. Sélectionnez le symbole, côté et type d'ordre
3. Entrez la quantité (et le prix pour les ordres limite)
4. Confirmez l'ordre

### 4. Monitoring
- Les données se mettent à jour automatiquement
- Les notifications apparaissent pour les événements importants
- Le statut de connexion est affiché en temps réel

## 🔌 API Endpoints

### Authentification
- `POST /api/auth/login` - Connexion utilisateur

### Données de Compte
- `GET /api/account/balance` - Solde du compte
- `GET /api/account/positions` - Positions ouvertes

### Données de Marché
- `GET /api/market/{symbol}` - Données de marché pour un symbole

### Trading
- `POST /api/trading/order` - Placer un ordre

### WebSocket
- `WS /ws` - Données en temps réel

## 🛠️ Développement

### Commandes Utiles
```bash
# Mode développement avec rechargement auto
make dev

# Tests de l'API
make test-api

# Vérification des prérequis
make check

# Nettoyage
make clean
```

### Personnalisation
1. **Styles**: Modifiez `static/css/style.css`
2. **JavaScript**: Éditez `static/js/trading.js`
3. **Templates**: Personnalisez les fichiers dans `templates/`
4. **Configuration**: Ajustez `web_config.py`

## 🔒 Sécurité

### Recommandations de Production
1. **Changez les identifiants par défaut**
2. **Utilisez HTTPS en production**
3. **Configurez un secret key fort**
4. **Limitez l'accès réseau si nécessaire**
5. **Activez les logs de sécurité**

### Authentification
- Tokens JWT avec expiration
- Hachage sécurisé des mots de passe
- Protection CSRF intégrée
- Validation des entrées utilisateur

## 📱 Responsive Design

L'interface s'adapte automatiquement à tous les écrans :
- **Desktop**: Interface complète avec sidebar
- **Tablet**: Layout adaptatif
- **Mobile**: Interface optimisée tactile

## 🚨 Dépannage

### Problèmes Courants

#### Port déjà utilisé
```bash
# Changer le port
python simple_app.py --port 8001
```

#### Erreur de connexion BingX
```bash
# Utiliser le mode démo
python simple_app.py
```

#### Problèmes d'authentification
- Vérifiez les identifiants par défaut
- Effacez le cache du navigateur
- Vérifiez la configuration des tokens

### Logs
```bash
# Afficher les logs
make logs

# Mode debug
make dev
```

## 🎉 Fonctionnalités Avancées

### Graphiques Interactifs
- Zoom et pan sur les graphiques
- Indicateurs techniques
- Historique des prix

### Notifications
- Alertes en temps réel
- Confirmations d'ordres
- Notifications de connexion

### Raccourcis Clavier
- `Ctrl+B`: Achat rapide
- `Ctrl+S`: Vente rapide
- `Escape`: Fermer les modales

## 📈 Prochaines Améliorations

- [ ] Indicateurs techniques avancés
- [ ] Alertes de prix personnalisées
- [ ] Historique détaillé des trades
- [ ] Export des données
- [ ] Mode multi-comptes
- [ ] Thèmes personnalisables

## 🆘 Support

Pour toute question ou problème :
1. Consultez cette documentation
2. Vérifiez les logs de l'application
3. Testez en mode démo
4. Vérifiez la configuration BingX

---

**🎯 L'application web BingX Trading est maintenant prête à utiliser !**
