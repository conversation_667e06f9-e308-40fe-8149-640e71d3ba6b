#!/usr/bin/env python3
"""
Script de lancement pour l'application web BingX Trading
"""

import os
import sys
import argparse
import uvicorn
import logging
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from webapp.config import webapp_config
    from webapp.main import app
except ImportError:
    # Fallback pour l'exécution directe
    from config import webapp_config
    from main import app


def setup_logging(level: str = "INFO"):
    """Configure le logging"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('webapp.log')
        ]
    )


def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""
    required_packages = [
        'fastapi', 'uvicorn', 'websockets', 'jinja2', 
        'python-multipart', 'python-jose', 'passlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Packages manquants:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n📦 Installez les dépendances avec:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def check_bingx_connection():
    """Vérifie la connexion à BingX"""
    try:
        try:
            from bingxapp.config import BingXConfig
            from bingxapp.client import BingXClient
        except ImportError:
            from config import BingXConfig
            from client import BingXClient

        config = BingXConfig.from_env()
        client = BingXClient(config)

        if client.test_connectivity():
            print("✅ Connexion BingX OK")
            return True
        else:
            print("⚠️  Problème de connexion BingX")
            return False

    except Exception as e:
        print(f"❌ Erreur de connexion BingX: {e}")
        return False


def create_directories():
    """Crée les répertoires nécessaires"""
    directories = [
        Path(__file__).parent / "static" / "css",
        Path(__file__).parent / "static" / "js",
        Path(__file__).parent / "static" / "img",
        Path(__file__).parent / "templates",
        Path(__file__).parent / "logs"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"📁 Répertoire créé: {directory}")


def show_startup_info():
    """Affiche les informations de démarrage"""
    print("🚀 Application Web BingX Trading")
    print("=" * 50)
    print(f"🌐 URL: http://{webapp_config.host}:{webapp_config.port}")
    print(f"🔧 Mode debug: {webapp_config.debug}")
    print(f"🔄 Rechargement auto: {webapp_config.reload}")
    print(f"👤 Utilisateur admin: {webapp_config.admin_username}")
    print(f"🔑 Mot de passe: {webapp_config.admin_password}")
    print("=" * 50)
    print("📋 Endpoints disponibles:")
    print("  - / : Page de connexion")
    print("  - /dashboard : Dashboard de trading")
    print("  - /api/auth/login : Authentification")
    print("  - /api/account/balance : Solde du compte")
    print("  - /api/account/positions : Positions ouvertes")
    print("  - /api/market/{symbol} : Données de marché")
    print("  - /api/trading/order : Placement d'ordres")
    print("  - /ws : WebSocket temps réel")
    print("=" * 50)


def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Application Web BingX Trading")
    parser.add_argument("--host", default=webapp_config.host, help="Adresse d'écoute")
    parser.add_argument("--port", type=int, default=webapp_config.port, help="Port d'écoute")
    parser.add_argument("--debug", action="store_true", help="Mode debug")
    parser.add_argument("--reload", action="store_true", help="Rechargement automatique")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--check-only", action="store_true", help="Vérifier seulement les dépendances")
    
    args = parser.parse_args()
    
    # Configuration du logging
    setup_logging(args.log_level)
    
    print("🔍 Vérification des prérequis...")
    
    # Vérifier les dépendances
    if not check_dependencies():
        sys.exit(1)
    
    # Créer les répertoires
    create_directories()
    
    # Vérifier la connexion BingX
    bingx_ok = check_bingx_connection()
    
    if args.check_only:
        if bingx_ok:
            print("✅ Tous les prérequis sont satisfaits")
            sys.exit(0)
        else:
            print("⚠️  Certains prérequis ne sont pas satisfaits")
            sys.exit(1)
    
    if not bingx_ok:
        print("⚠️  Attention: Problème de connexion BingX détecté")
        response = input("Continuer quand même ? (y/N): ").strip().lower()
        if response != 'y':
            sys.exit(1)
    
    # Afficher les informations de démarrage
    show_startup_info()
    
    # Mettre à jour la configuration
    webapp_config.host = args.host
    webapp_config.port = args.port
    webapp_config.debug = args.debug or webapp_config.debug
    webapp_config.reload = args.reload or webapp_config.reload
    
    try:
        print("🚀 Démarrage du serveur...")
        
        # Lancer le serveur
        uvicorn.run(
            "webapp.main:app",
            host=webapp_config.host,
            port=webapp_config.port,
            reload=webapp_config.reload,
            log_level=args.log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur demandé par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
