#!/usr/bin/env python3
"""
Script de lancement simple pour l'application web BingX Trading
"""

import os
import sys
import uvicorn
import logging
from pathlib import Path

# Ajouter les chemins nécessaires
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))
sys.path.insert(0, str(current_dir))

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def check_dependencies():
    """Vérifie les dépendances"""
    try:
        import fastapi
        import uvicorn
        import websockets
        import jinja2
        print("✅ Dépendances web OK")
        return True
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        return False

def check_bingx():
    """Vérifie la connexion BingX"""
    try:
        # Import depuis le répertoire parent
        sys.path.insert(0, str(parent_dir))

        import config as bingx_config
        import client

        config = bingx_config.BingXConfig.from_env()
        bingx_client = client.BingXClient(config)

        if bingx_client.test_connectivity():
            print("✅ Connexion BingX OK")
            return True
        else:
            print("⚠️  Problème de connexion BingX")
            return False
    except Exception as e:
        print(f"❌ Erreur BingX: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Démarrage de l'application web BingX Trading")
    print("=" * 60)
    
    # Vérifications
    if not check_dependencies():
        print("Installez les dépendances avec: pip install -r requirements.txt")
        sys.exit(1)
    
    bingx_ok = check_bingx()
    if not bingx_ok:
        print("⚠️  Attention: Problème de connexion BingX")
        response = input("Continuer quand même ? (y/N): ").strip().lower()
        if response != 'y':
            sys.exit(1)
    
    # Configuration
    host = "0.0.0.0"
    port = 8000
    
    print(f"🌐 Démarrage sur http://{host}:{port}")
    print("👤 Identifiants par défaut: admin / admin123")
    print("🛑 Appuyez sur Ctrl+C pour arrêter")
    print("=" * 60)
    
    try:
        # Importer l'application depuis le module local
        import importlib.util
        spec = importlib.util.spec_from_file_location("webapp_main", current_dir / "main.py")
        webapp_main = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(webapp_main)
        app = webapp_main.app
        
        # Lancer le serveur
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
