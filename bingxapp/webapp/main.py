"""
Application web principale BingX Trading
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAP<PERSON>, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi import Request
from pydantic import BaseModel
import uvicorn

# Import des modules BingX existants
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import config as bingx_config
import trading_bot
import client

BingXConfig = bingx_config.BingXConfig
TradingBot = trading_bot.TradingBot
BingXClient = client.BingXClient

from web_config import webapp_config
from auth import (
    authenticate_user, create_access_token, get_current_active_user,
    Token, User
)


# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialisation de l'application FastAPI
app = FastAPI(
    title="BingX Trading Web App",
    description="Interface web moderne pour le trading sur BingX",
    version="1.0.0"
)

# Configuration des templates et fichiers statiques
templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

# Instance globale du bot de trading
trading_bot: Optional[TradingBot] = None
websocket_connections: List[WebSocket] = []


# Modèles Pydantic pour l'API
class OrderRequest(BaseModel):
    symbol: str
    side: str  # BUY ou SELL
    order_type: str  # MARKET ou LIMIT
    quantity: float
    price: Optional[float] = None


class AlertRequest(BaseModel):
    symbol: str
    condition: str  # "above" ou "below"
    price: float
    message: Optional[str] = None


class MarketDataResponse(BaseModel):
    symbol: str
    price: float
    change_24h: float
    volume_24h: float
    timestamp: datetime


# Initialisation du bot de trading
async def initialize_trading_bot():
    """Initialise le bot de trading"""
    global trading_bot
    try:
        config = BingXConfig.from_env()
        trading_bot = TradingBot(config)
        
        if trading_bot.initialize():
            logger.info("Bot de trading initialisé avec succès")
            return True
        else:
            logger.error("Échec de l'initialisation du bot de trading")
            return False
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation du bot: {e}")
        return False


# Gestionnaire de connexions WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Nettoyer les connexions fermées
        for conn in disconnected:
            self.disconnect(conn)


manager = ConnectionManager()


# Routes d'authentification
@app.post("/api/auth/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """Connexion et génération de token"""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=webapp_config.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


# Routes de l'API
@app.get("/api/account/balance")
async def get_account_balance(current_user: User = Depends(get_current_active_user)):
    """Récupère le solde du compte"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")
    
    try:
        balance = trading_bot.get_account_balance()
        return balance
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du solde: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/account/positions")
async def get_positions(current_user: User = Depends(get_current_active_user)):
    """Récupère les positions ouvertes"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")
    
    try:
        positions = trading_bot.get_positions()
        return {"positions": positions}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des positions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/market/{symbol}")
async def get_market_data(symbol: str, current_user: User = Depends(get_current_active_user)):
    """Récupère les données de marché pour un symbole"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")
    
    try:
        market_data = trading_bot.get_market_data(symbol)
        return market_data
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des données de marché: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/trading/order")
async def place_order(order: OrderRequest, current_user: User = Depends(get_current_active_user)):
    """Place un ordre de trading"""
    if not trading_bot:
        raise HTTPException(status_code=500, detail="Bot de trading non initialisé")
    
    try:
        if order.order_type.upper() == "MARKET":
            result = trading_bot.place_market_order(order.symbol, order.side, order.quantity)
        elif order.order_type.upper() == "LIMIT":
            if order.price is None:
                raise HTTPException(status_code=400, detail="Prix requis pour un ordre limite")
            result = trading_bot.place_limit_order(order.symbol, order.side, order.quantity, order.price)
        else:
            raise HTTPException(status_code=400, detail="Type d'ordre non supporté")
        
        if result:
            # Notifier via WebSocket
            await manager.broadcast(json.dumps({
                "type": "order_placed",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }))
            return result
        else:
            raise HTTPException(status_code=500, detail="Échec du placement de l'ordre")
            
    except Exception as e:
        logger.error(f"Erreur lors du placement de l'ordre: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Route principale
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Page d'accueil"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Dashboard de trading"""
    return templates.TemplateResponse("dashboard.html", {"request": request})


# WebSocket pour les données en temps réel
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket pour les mises à jour en temps réel"""
    await manager.connect(websocket)
    try:
        while True:
            # Envoyer des données de marché périodiquement
            if trading_bot:
                try:
                    # Données de marché pour BTC-USDT
                    market_data = trading_bot.get_market_data("BTC-USDT")
                    if market_data:
                        await manager.send_personal_message(
                            json.dumps({
                                "type": "market_data",
                                "symbol": "BTC-USDT",
                                "data": market_data,
                                "timestamp": datetime.now().isoformat()
                            }),
                            websocket
                        )
                    
                    # Positions
                    positions = trading_bot.get_positions()
                    await manager.send_personal_message(
                        json.dumps({
                            "type": "positions",
                            "data": positions,
                            "timestamp": datetime.now().isoformat()
                        }),
                        websocket
                    )
                    
                except Exception as e:
                    logger.error(f"Erreur WebSocket: {e}")
            
            await asyncio.sleep(webapp_config.market_data_update_interval)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)


# Événement de démarrage
@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    logger.info("Démarrage de l'application web BingX Trading")
    await initialize_trading_bot()


# Événement d'arrêt
@app.on_event("shutdown")
async def shutdown_event():
    """Nettoyage à l'arrêt"""
    logger.info("Arrêt de l'application web BingX Trading")
    if trading_bot:
        trading_bot.stop()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=webapp_config.host,
        port=webapp_config.port,
        reload=webapp_config.reload,
        log_level="info"
    )
