/* ========================================
   Composants Réutilisables TradingAgents
   ======================================== */

/* ========================================
   Métriques Cards
   ======================================== */

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.metric-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary-color);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.metric-icon.success {
  background: linear-gradient(135deg, var(--success-color), #34d399);
}

.metric-icon.warning {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.metric-icon.error {
  background: linear-gradient(135deg, var(--error-color), #f87171);
}

.metric-icon.info {
  background: linear-gradient(135deg, var(--info-color), #60a5fa);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--space-1);
  font-family: var(--font-mono);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.metric-change {
  font-size: var(--text-xs);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--error-color);
}

.metric-change.neutral {
  color: var(--text-muted);
}

/* ========================================
   Checkbox Cards
   ======================================== */

.checkbox-card {
  position: relative;
  margin-bottom: var(--space-3);
}

.checkbox-card input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
}

.checkbox-card input[type="checkbox"]:checked + .checkbox-label {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.checkbox-label:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.checkbox-label i {
  font-size: var(--text-lg);
  width: 24px;
  text-align: center;
}

/* ========================================
   Status Badges
   ======================================== */

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.online {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.offline {
  background-color: rgba(107, 114, 128, 0.1);
  color: var(--text-muted);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-badge.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
}

/* ========================================
   Action Buttons
   ======================================== */

.action-buttons {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.btn-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  background-color: var(--bg-card);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.btn-icon:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-icon.danger:hover {
  background-color: var(--error-color);
  border-color: var(--error-color);
}

.btn-icon.success:hover {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

/* ========================================
   Data Tables
   ======================================== */

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th {
  background-color: var(--bg-secondary);
  padding: var(--space-4);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--border-color);
}

.data-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.data-table tr:hover {
  background-color: var(--bg-secondary);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* ========================================
   Section Headers
   ======================================== */

.section-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  color: var(--text-inverse);
  padding: var(--space-12) 0;
  margin-bottom: var(--space-8);
  border-radius: var(--radius-2xl);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50px, -50px);
}

.section-header h1 {
  font-size: var(--text-4xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
}

.section-header p {
  font-size: var(--text-lg);
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* ========================================
   Alerts
   ======================================== */

.alert {
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: var(--error-color);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: var(--info-color);
}

.alert h5 {
  margin: 0 0 var(--space-2) 0;
  font-weight: 600;
}

.alert h5 i {
  margin-right: var(--space-2);
}

/* ========================================
   Badges
   ======================================== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.badge-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.badge-success {
  background-color: var(--success-color);
  color: var(--text-inverse);
}

.badge-warning {
  background-color: var(--warning-color);
  color: var(--text-inverse);
}

.badge-error {
  background-color: var(--error-color);
  color: var(--text-inverse);
}

/* ========================================
   Loading Spinner
   ======================================== */

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: currentColor;
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--space-2);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ========================================
   Data Table Container
   ======================================== */

.data-table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

/* ========================================
   Responsive Design
   ======================================== */

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .metric-card {
    padding: var(--space-4);
  }
  
  .metric-icon {
    width: 50px;
    height: 50px;
    font-size: var(--text-lg);
  }
  
  .metric-value {
    font-size: var(--text-2xl);
  }
  
  .section-header {
    padding: var(--space-8) 0;
  }
  
  .section-header h1 {
    font-size: var(--text-3xl);
  }
  
  .action-buttons {
    flex-wrap: wrap;
  }
  
  .data-table {
    font-size: var(--text-sm);
  }
  
  .data-table th,
  .data-table td {
    padding: var(--space-3);
  }
}
